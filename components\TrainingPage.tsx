

import React, { useState, useEffect, useRef, useCallback, useMemo, FC } from 'react';
import { ArchetypeSession, ActivePage, Question, User, ProblemArchetypeDetails, QuestionType, BreakthroughSessionState } from '../types';
import * as api from '../services/apiService';
import { 
    XCircleIcon, CheckCircleIcon, FlagIcon, ArrowPathIcon, XMarkIcon, CheckBadgeIcon, BeakerIcon, ExclamationTriangleIcon, SparklesIcon, BookOpenIcon, ChevronDownIcon, ClockIcon, ArrowRightIcon
} from '@heroicons/react/24/solid';
import MathRenderer from './MarkdownKatex';


// --- Types for internal state machine ---
type FlowState = 
    'LOADING_INITIAL' | 
    'ASSESSING_INITIAL' |
    'BREAKTHROUGH_FOUND' |
    'LOADING_DRILLDOWN' |
    'ASSESSING_DRILLDOWN' |
    'RETESTING' |
    'CONSOLIDATING' |
    'FINISHED' | 
    'CONFIRM_EXIT' | 
    'ERROR';

interface SelectionState {
    visible: boolean;
    text: string;
    x: number;
    y: number;
}


// --- Helper Functions ---
const normalizeAnswer = (answer: string) => {
    return answer.replace(/\s+/g, '').replace(/\\frac{([^}]+)}{([^}]+)}/g, '$1/$2').replace(/[{()}]/g, '');
};

const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
};


// --- Child Components ---
const FeedbackModal: React.FC<{ onClose: () => void }> = ({ onClose }) => (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-2xl p-6 w-full max-w-sm text-center transform transition-all animate-fade-in-fast">
            <CheckBadgeIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-gray-900">反馈已提交</h3>
            <p className="text-gray-600 my-4">感谢你的反馈！我们会尽快检查并处理此问题。</p>
            <button onClick={onClose} className="px-8 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 font-semibold shadow-sm transition transform hover:scale-105">关闭</button>
        </div>
    </div>
);

const ConfirmationModal: React.FC<{ title: string, message: string, confirmText: string, cancelText?: string, onConfirm: () => void, onCancel: () => void }> = ({ title, message, confirmText, cancelText = "取消", onConfirm, onCancel }) => (
     <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-[100] p-4">
        <div className="bg-white rounded-lg shadow-2xl p-6 w-full max-w-md text-center">
             <ExclamationTriangleIcon className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
             <h3 className="text-xl font-bold text-gray-900">{title}</h3>
             <p className="text-gray-600 my-4">{message}</p>
             <div className="flex justify-center gap-4">
                <button onClick={onCancel} className="px-6 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 font-semibold">{cancelText}</button>
                <button onClick={onConfirm} className="px-6 py-2 rounded-lg bg-red-500 text-white hover:bg-red-600 font-semibold">{confirmText}</button>
             </div>
        </div>
     </div>
);

const AskAiPopup: React.FC<{ state: SelectionState; onAsk: (text: string) => void }> = ({ state, onAsk }) => {
    if (!state.visible) return null;
    return (
        <div className="fixed z-50 animate-fade-in-fast" style={{ top: `${state.y}px`, left: `${state.x}px` }}>
            <button onClick={() => onAsk(state.text)} className="bg-blue-600 text-white font-bold py-1.5 px-3 rounded-lg shadow-lg flex items-center gap-2 hover:bg-blue-700 transition transform hover:scale-105">
                <SparklesIcon className="w-5 h-5" /> 询问 AI
            </button>
        </div>
    );
};


// --- Main Page Component ---
interface TrainingPageProps {
  session?: ArchetypeSession;
  activeBreakthroughSession?: BreakthroughSessionState;
  user: User;
  onNavigate: (page: ActivePage, context?: any) => void;
  onOpenAiPanel: (initialPrompt?: string) => void;
}

const TrainingPage: React.FC<TrainingPageProps> = ({ session, activeBreakthroughSession, user, onNavigate, onOpenAiPanel }) => {
    const [flowState, setFlowState] = useState<FlowState>('LOADING_INITIAL');
    const [previousFlowState, setPreviousFlowState] = useState<FlowState>('LOADING_INITIAL');
    
    const [queue, setQueue] = useState<Question[]>([]);
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [processedQuestion, setProcessedQuestion] = useState<Question | null>(null);
    const [isAiProcessing, setIsAiProcessing] = useState(false);
    
    const [userAnswer, setUserAnswer] = useState('');
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isCorrect, setIsCorrect] = useState(false);
    const [wrongAttempts, setWrongAttempts] = useState(0);
    const [apiError, setApiError] = useState('');

    const [assessmentTimeLeft, setAssessmentTimeLeft] = useState(300); // 5 minutes timer

    const [breakthroughArchetype, setBreakthroughArchetype] = useState<ProblemArchetypeDetails | null>(null);
    const [questionForRetest, setQuestionForRetest] = useState<Question | null>(null);
    const [isSynopsisVisible, setIsSynopsisVisible] = useState(false);


    const [showFeedbackModal, setShowFeedbackModal] = useState(false);
    const [selectionState, setSelectionState] = useState<SelectionState>({ visible: false, text: '', x: 0, y: 0 });
    const solutionContainerRef = useRef<HTMLDivElement>(null);

    const resetQuestionState = useCallback(() => {
        setUserAnswer('');
        setIsSubmitted(false);
        setIsCorrect(false);
        setWrongAttempts(0);
        setAssessmentTimeLeft(300); // Reset timer for next assessment question
        setProcessedQuestion(null);
    }, []);

    const enterBreakthroughFoundState = useCallback(async (failedQuestion: Question) => {
        setFlowState('LOADING_DRILLDOWN');
        try {
            const details = await api.getArchetypeDetails(failedQuestion.archetypeId);
            setBreakthroughArchetype(details);
            // The failedQuestion is already processed and has the `isProcessedByAI` flag.
            // This is our cache for the breakthrough question.
            setQuestionForRetest(failedQuestion); 
            setFlowState('BREAKTHROUGH_FOUND');
        } catch(e) {
            setApiError(e instanceof Error ? e.message : '获取薄弱点详情失败。');
            setFlowState('ERROR');
        }
    }, []);
    
    const initiateBreakthroughTraining = useCallback(async (failedQuestion: Question) => {
        setFlowState('LOADING_DRILLDOWN'); 
        try {
            const details = await api.getArchetypeDetails(failedQuestion.archetypeId);
            setBreakthroughArchetype(details);
            setQuestionForRetest(failedQuestion); // This is now the processed question
            setQueue([failedQuestion]); 
            setCurrentQuestionIndex(0);
            resetQuestionState();
            setIsSynopsisVisible(false); 
            setFlowState('RETESTING'); 
        } catch(e) {
            setApiError(e instanceof Error ? e.message : '获取复习资料失败。');
            setFlowState('ERROR');
        }
    }, [resetQuestionState]);

    
    const startDrillDown = useCallback(async (failedQuestion: Question) => {
        setFlowState('LOADING_DRILLDOWN');
        try {
            const drillDownQuestions = await api.queryQuestionAndScoreReduce(user.id, failedQuestion.archetypeId);
            if (drillDownQuestions.length > 0) {
                setQueue(drillDownQuestions);
                setCurrentQuestionIndex(0);
                resetQuestionState();
                setFlowState('ASSESSING_DRILLDOWN');
            } else {
                enterBreakthroughFoundState(failedQuestion);
            }
        } catch (e) {
            setApiError(e instanceof Error ? e.message : '深入探索失败。');
            setFlowState('ERROR');
        }
    }, [user.id, resetQuestionState, enterBreakthroughFoundState]);

    const startInitialAssessment = useCallback(async () => {
        if (!session?.topic?.id) {
            setApiError('未指定训练专题，无法开始。');
            setFlowState('ERROR');
            return;
        }
        setFlowState('LOADING_INITIAL');
        setApiError('');
        setBreakthroughArchetype(null); 
        
        let initialQuestions: Question[] = [];
        while(initialQuestions.length === 0) {
            try {
                initialQuestions = await api.getInitialAssessmentQuestions(session.topic.id);
                if (initialQuestions.length === 0) {
                    await new Promise(resolve => setTimeout(resolve, 1000)); 
                }
            } catch (error) {
                console.error("Retrying initial assessment fetch...", error);
                await new Promise(resolve => setTimeout(resolve, 2000)); 
            }
        }
        
        setQueue(initialQuestions);
        setCurrentQuestionIndex(0);
        resetQuestionState();
        setFlowState('ASSESSING_INITIAL');

    }, [session, resetQuestionState]);

    const initiateBreakthroughTrainingFromContext = useCallback(async (breakthroughCtx: { topicId: string; archetypeId: string; }) => {
        setFlowState('LOADING_DRILLDOWN');
        try {
            const details = await api.getArchetypeDetails(breakthroughCtx.archetypeId);
            setBreakthroughArchetype(details);
            const questions = await api.getRandomQuestionForArchetype(breakthroughCtx.archetypeId, breakthroughCtx.topicId);
            if (questions.length === 0) throw new Error("Could not find questions for this archetype.");
            setQueue(questions);
            setCurrentQuestionIndex(0);
            resetQuestionState();
            setIsSynopsisVisible(true);
            setFlowState('RETESTING');
        } catch(e) {
             setApiError(e instanceof Error ? e.message : '加载上次的训练失败。');
             setFlowState('ERROR');
        }
    }, [resetQuestionState]);
    
    useEffect(() => {
        if (activeBreakthroughSession) {
            initiateBreakthroughTrainingFromContext(activeBreakthroughSession);
        } else if (session?.topic) {
            startInitialAssessment();
        } else {
            setApiError('未指定训练专题，无法开始。请从“训练”页面选择一个专题。');
            setFlowState('ERROR');
        }
    }, [session, activeBreakthroughSession, startInitialAssessment, initiateBreakthroughTrainingFromContext]);

    const currentRawQuestion = useMemo(() => queue[currentQuestionIndex], [queue, currentQuestionIndex]);

    useEffect(() => {
        if (!currentRawQuestion) {
            setProcessedQuestion(null);
            return;
        }

        // If the question object is already processed, don't call AI again.
        if (currentRawQuestion.isProcessedByAI) {
            setProcessedQuestion(currentRawQuestion);
            setIsAiProcessing(false);
            return;
        }
        
        setIsAiProcessing(true);
        setProcessedQuestion(null);

        api.preprocessAndValidateQuestion(currentRawQuestion)
            .then(processedQ => {
                // Add a flag to indicate this question has been processed by AI
                setProcessedQuestion({ ...processedQ, isProcessedByAI: true });
            })
            .catch(err => {
                console.error("AI preprocessing failed, using raw question as fallback.", err);
                setProcessedQuestion(currentRawQuestion); // Use raw question on error
            })
            .finally(() => {
                setIsAiProcessing(false);
            });

    }, [currentRawQuestion]);

    const handleCorrectAnswer = useCallback(async () => {
        const nextIndex = currentQuestionIndex + 1;
        
        if (flowState === 'ASSESSING_INITIAL') {
            if (nextIndex < queue.length) {
                setCurrentQuestionIndex(nextIndex);
                resetQuestionState();
            } else {
                 if (queue.length < 2) {
                    setFlowState('LOADING_INITIAL');
                    try {
                        const consolidationQ = await api.getRandomQuestionForArchetype(processedQuestion!.archetypeId, processedQuestion!.topicId);
                        if (consolidationQ.length > 0) {
                            setQueue(consolidationQ);
                            setCurrentQuestionIndex(0);
                            resetQuestionState();
                            setFlowState('ASSESSING_INITIAL');
                        } else {
                            startInitialAssessment();
                        }
                    } catch (e) { startInitialAssessment(); }
                 } else {
                    setFlowState('FINISHED');
                 }
            }
        }
        else if (flowState === 'ASSESSING_DRILLDOWN') {
             if (nextIndex < queue.length) {
                setCurrentQuestionIndex(nextIndex);
                resetQuestionState();
            } else {
                enterBreakthroughFoundState(processedQuestion!);
            }
        }
        else if (flowState === 'RETESTING' || flowState === 'CONSOLIDATING') {
             setFlowState('FINISHED');
        }
    }, [flowState, queue, processedQuestion, currentQuestionIndex, resetQuestionState, startInitialAssessment, enterBreakthroughFoundState]);
    
    const handleWrongAnswer = useCallback(() => {
        if (flowState === 'ASSESSING_INITIAL') {
            startDrillDown(processedQuestion!);
        }
        else if (flowState === 'ASSESSING_DRILLDOWN') {
            enterBreakthroughFoundState(processedQuestion!);
        }
        else if (flowState === 'CONSOLIDATING') {
            if (questionForRetest) {
                // This logic is optimized to prevent re-processing of an already-seen question.
                // By setting the processedQuestion directly, we avoid the flash of the loader
                // that happens when the state is reset and useEffect has to catch up.
                setQueue([questionForRetest]);
                setCurrentQuestionIndex(0);
                setProcessedQuestion(questionForRetest); // Set the processed question directly
                setUserAnswer('');
                setIsSubmitted(false);
                setIsCorrect(false);
                setWrongAttempts(0);
                setIsSynopsisVisible(true);
                setFlowState('RETESTING');
            } else {
                startInitialAssessment();
            }
        }
    }, [flowState, processedQuestion, questionForRetest, startDrillDown, startInitialAssessment, resetQuestionState, enterBreakthroughFoundState]);

    const handleSubmitAnswer = useCallback(async () => {
        if (!processedQuestion) return;
        setIsSubmitted(true);
        const correct = processedQuestion.type === '单选题'
            ? userAnswer === processedQuestion.standardAnswer
            : normalizeAnswer(userAnswer) === normalizeAnswer(processedQuestion.standardAnswer);

        setIsCorrect(correct);
        
        const timeout = 1500;
        if (correct) {
            api.addScoreForCorrectAnswer(user.id, processedQuestion.archetypeId).catch(console.error);
            setTimeout(() => handleCorrectAnswer(), timeout);
        } else {
            const newWrongAttempts = wrongAttempts + 1;
            setWrongAttempts(newWrongAttempts);
    
            // In training mode (RETESTING), don't advance automatically on wrong answer.
            // Let the UI handle showing retry/solution options.
            if (flowState !== 'RETESTING') {
                 setTimeout(() => handleWrongAnswer(), timeout);
            }
        }
    }, [processedQuestion, userAnswer, flowState, wrongAttempts, user.id, handleCorrectAnswer, handleWrongAnswer]);

    const handleContinueToConsolidation = useCallback(async () => {
        if (!processedQuestion) return;
        setFlowState('LOADING_DRILLDOWN');
        try {
            const newQ = await api.getRandomQuestionForArchetype(processedQuestion.archetypeId, processedQuestion.topicId);
            if (newQ.length > 0) {
                 setQueue(newQ);
                 setCurrentQuestionIndex(0);
                 resetQuestionState();
                 setFlowState('CONSOLIDATING');
            } else { 
                // Fallback if no new question is found, end the session successfully.
                setFlowState('FINISHED');
            }
        } catch(e) { 
             console.error("Failed to fetch consolidation question", e);
             setFlowState('FINISHED');
        }
    }, [processedQuestion, resetQuestionState]);

    useEffect(() => {
        // Timer should only run during active assessment phases when no answer is submitted.
        const isAssessmentActive = (flowState === 'ASSESSING_INITIAL' || flowState === 'ASSESSING_DRILLDOWN') && !isSubmitted;
        
        if (!isAssessmentActive) {
            return;
        }

        if (assessmentTimeLeft <= 0) {
            handleSubmitAnswer();
            return;
        }

        const timerId = setInterval(() => {
            setAssessmentTimeLeft(prev => prev - 1);
        }, 1000);

        return () => clearInterval(timerId);
    }, [assessmentTimeLeft, flowState, isSubmitted, handleSubmitAnswer]);


    useEffect(() => {
        const handleTextSelection = () => {
            const container = solutionContainerRef.current;
            if (!container) return;
            const selection = window.getSelection();
            if (!selection || selection.rangeCount === 0 || !container.contains(selection.anchorNode)) {
                 if (selectionState.visible) setSelectionState(s => ({ ...s, visible: false }));
                 return;
            }
            const text = selection.toString().trim();
            if (text.length > 2) {
                const range = selection.getRangeAt(0);
                const rect = range.getBoundingClientRect();
                setSelectionState({ visible: true, text: text, x: rect.left + window.scrollX, y: rect.top + window.scrollY - 40 });
            } else {
                 if (selectionState.visible) setSelectionState(s => ({ ...s, visible: false }));
            }
        };
        document.addEventListener('mouseup', handleTextSelection);
        return () => document.removeEventListener('mouseup', handleTextSelection);
    }, [selectionState.visible]);
    
    const handleRetry = () => {
        setUserAnswer('');
        setIsSubmitted(false);
    };
    
    const handleAiQueryFromSelection = useCallback((text: string) => {
        setSelectionState(s => ({ ...s, visible: false }));
        const prompt = `对于题目“${processedQuestion?.questionText}”，请解释一下答案解析中的这一步：“${text}”`;
        onOpenAiPanel(prompt);
    }, [processedQuestion, onOpenAiPanel]);

    const triggerExitConfirmation = () => {
        setPreviousFlowState(flowState);
        setFlowState('CONFIRM_EXIT');
    };
    
    const onConfirmExit = () => {
        onNavigate(ActivePage.AssessmentCenter);
    };

    const renderHeader = () => {
        const isTrainingMode = flowState === 'RETESTING' || flowState === 'CONSOLIDATING';
        const isAssessmentMode = flowState === 'ASSESSING_INITIAL' || flowState === 'ASSESSING_DRILLDOWN';

        return (
            <div className="w-full max-w-5xl mx-auto mb-6">
                <div className="bg-white p-4 rounded-xl shadow-md border border-gray-200 overflow-hidden">
                    <div className="flex justify-between items-center px-2">
                        <button onClick={() => setShowFeedbackModal(true)} className="flex items-center gap-1.5 text-sm font-semibold text-gray-600 hover:text-red-600 transition-colors w-24">
                            <FlagIcon className="w-5 h-5" /> 反馈
                        </button>
                        
                        <div className="text-center">
                            {isTrainingMode && (
                                <div className="flex flex-col items-center animate-fade-in-fast">
                                    <h2 className="text-2xl font-black text-blue-600 tracking-wider">专项训练</h2>
                                    <p className="text-sm text-gray-500 mt-1">针对薄弱题型进行巩固</p>
                                </div>
                            )}
                            {isAssessmentMode && !isSubmitted && (
                                <div className="flex flex-col items-center animate-fade-in-fast">
                                    <div className={`flex items-center gap-2 font-mono text-3xl font-bold ${assessmentTimeLeft < 60 ? 'text-red-500 animate-pulse' : 'text-gray-900'}`}>
                                        <ClockIcon className="w-7 h-7" />
                                        <span>{formatTime(assessmentTimeLeft)}</span>
                                    </div>
                                    <p className="text-sm text-gray-500 mt-1">寻找学习突破口</p>
                                </div>
                            )}
                            {isAssessmentMode && isSubmitted && (
                                <div className="flex flex-col items-center animate-fade-in-fast h-[56px] justify-center">
                                    <h2 className="text-xl font-bold text-gray-700">正在分析...</h2>
                                </div>
                            )}
                        </div>
                        
                        <button onClick={triggerExitConfirmation} className="text-sm font-semibold text-gray-600 hover:text-red-600 transition-colors w-24 text-right">
                            退出
                        </button>
                    </div>
                </div>
            </div>
        );
    };
    
    const renderAssessmentFooter = () => {
        // This component is no longer needed as retries are handled in the Training footer logic
        return null;
    };

    const renderTrainingFooter = () => {
        if (flowState !== 'RETESTING' && flowState !== 'CONSOLIDATING') return null;
    
        return (
            <>
                <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <div className="flex justify-between items-center">
                        <button 
                            onClick={() => setIsSynopsisVisible(!isSynopsisVisible)}
                            className="font-semibold text-blue-600 flex items-center gap-2 py-2 px-3 rounded-lg hover:bg-blue-100 transition-colors"
                        >
                            <BookOpenIcon className="w-5 h-5" />
                            复习提要
                            <ChevronDownIcon className={`w-4 h-4 transition-transform ${isSynopsisVisible ? 'rotate-180' : ''}`} />
                        </button>
                        
                        {!isSubmitted &&
                            <button onClick={handleSubmitAnswer} disabled={userAnswer.trim() === ''}
                                className="px-8 py-3 bg-green-500 text-white font-bold rounded-md text-lg hover:bg-green-600 disabled:bg-gray-400 transition-colors whitespace-nowrap">
                                {processedQuestion?.type === '单选题' ? '确认选择' : '提交答案'}
                            </button>
                        }
                         {isSubmitted && !isCorrect && (flowState === 'RETESTING' || flowState === 'CONSOLIDATING') && wrongAttempts >= 2 &&
                            <button onClick={handleContinueToConsolidation} className="px-8 py-3 bg-blue-600 text-white font-bold rounded-md text-lg hover:bg-blue-700 transition-colors animate-fade-in-fast">
                                继续巩固
                            </button>
                        }
                    </div>
                </div>

                {isSynopsisVisible && breakthroughArchetype && (
                    <div className="mt-4 animate-fade-in-fast">
                        <div ref={solutionContainerRef} className="space-y-2 text-left bg-blue-50/50 p-6 rounded-lg border border-gray-200 shadow-sm prose">
                            <p className="font-bold text-blue-800 text-lg">{breakthroughArchetype.archetypeType}</p>
                            <div className="text-gray-800 text-base mt-2">
                               <MathRenderer content={breakthroughArchetype.description || ''} />
                            </div>
                        </div>
                    </div>
                )}
            </>
        );
    };
    
    
    const renderQuestionArea = () => {
        if (!processedQuestion) return null;
        
        const isAssessmentMode = flowState === 'ASSESSING_INITIAL' || flowState === 'ASSESSING_DRILLDOWN';

        const { questionText, options, archetypeId, type, standardAnswer, detailedSolution } = processedQuestion;

        const renderOptions = () => {
            if (!options) return null;
            
            return (
                <div className="space-y-3 mt-6">
                    {options.map((option, index) => {
                        const letter = String.fromCharCode(65 + index);
                        const isSelected = userAnswer === letter;

                        // Determine styling based on state
                        let styleClasses = "border-gray-300 hover:border-blue-500 hover:bg-blue-50";
                        if (isSubmitted) {
                            const isCorrectAnswer = standardAnswer === letter;
                            if (isSelected && !isCorrectAnswer) {
                                styleClasses = "border-red-400 bg-red-100 text-red-800"; // Wrong selected
                            } else if (isCorrectAnswer) {
                                styleClasses = "border-green-400 bg-green-100 text-green-800"; // Correct answer
                            } else {
                                styleClasses = "border-gray-300 bg-gray-50 opacity-70"; // Not selected, disabled look
                            }
                        } else if (isSelected) {
                            styleClasses = "border-blue-500 bg-blue-100 ring-2 ring-blue-300"; // Actively selected
                        }

                        return (
                            <div
                                key={letter}
                                onClick={() => !isSubmitted && setUserAnswer(letter)}
                                className={`flex items-start p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer ${styleClasses}`}
                                role="radio"
                                aria-checked={isSelected}
                                tabIndex={isSubmitted ? -1 : 0}
                            >
                                <span className="font-bold text-lg mr-4">{letter}.</span>
                                <div className="text-lg flex-grow">
                                    <MathRenderer content={option.replace(/^[A-Z]\.\s*/, '')} />
                                </div>
                            </div>
                        );
                    })}
                </div>
            );
        };

        const renderTextarea = () => (
             <div className="mt-8 w-full">
                <p className="text-sm font-semibold text-gray-600 mb-2">请输入你的答案 (如果没有思路，请输入“不知道”)</p>
                <textarea
                    value={userAnswer}
                    onChange={(e) => setUserAnswer(e.target.value)}
                    disabled={isSubmitted}
                    rows={3}
                    className="w-full p-3 border-2 border-gray-300 rounded-md text-lg focus:border-blue-500 focus:ring-blue-500 transition-colors disabled:bg-gray-100"
                />
                <p className="text-xs text-gray-500 mt-1">答案为数值形式，如 4, 0.3, 2.5, -5/2。不包含特殊字符，如=, 括号, π。</p>
            </div>
        );


        return (
            <div className="bg-transparent rounded-lg p-6 md:p-8">
                <div>
                    <div className="text-xs text-gray-400 font-mono mb-2">Archetype ID: {archetypeId}</div>
                    <div className="text-xl max-w-none text-gray-800 prose prose-xl">
                      <MathRenderer content={questionText} />
                    </div>
                    {type === '单选题' ? renderOptions() : renderTextarea()}

                    {isAssessmentMode && !isSubmitted && (
                        <div className="text-center mt-6">
                            <button onClick={handleSubmitAnswer} disabled={userAnswer.trim() === ''}
                                className="px-8 py-3 bg-green-500 text-white font-bold rounded-md text-lg hover:bg-green-600 disabled:bg-gray-400 transition-colors whitespace-nowrap">
                                {processedQuestion.type === '单选题' ? '确认选择' : '提交答案'}
                            </button>
                        </div>
                    )}
                    
                    {isSubmitted && isCorrect && (
                         <div className="mt-6 p-4 bg-green-100 text-green-700 font-bold flex items-center rounded-lg animate-fade-in-fast">
                             <CheckCircleIcon className="w-6 h-6 mr-2"/> 回答正确！
                         </div>
                    )}
                    
                    {isSubmitted && !isCorrect && (flowState === 'ASSESSING_INITIAL' || flowState === 'ASSESSING_DRILLDOWN' || flowState === 'CONSOLIDATING') && (
                         <div className="mt-6 p-4 bg-red-100 text-red-700 font-bold flex items-center rounded-lg animate-fade-in-fast">
                             <XCircleIcon className="w-6 h-6 mr-2"/> 回答错误
                         </div>
                    )}

                    {isSubmitted && !isCorrect && flowState === 'RETESTING' && (
                        <div className="mt-6 animate-fade-in-fast">
                             <div className="p-4 bg-red-100 text-red-700 font-bold flex items-center rounded-t-lg">
                                <XCircleIcon className="w-6 h-6 mr-2"/> 回答错误
                            </div>

                            {wrongAttempts < 2 ? (
                                <div className="p-6 bg-gray-50 rounded-b-lg border-x border-b border-gray-200 text-center">
                                    <p className="text-gray-700 mb-4">答案不正确。别灰心，再试一次！</p>
                                    <button onClick={handleRetry} className="px-8 py-3 bg-red-500 text-white font-bold rounded-md text-lg hover:bg-red-600 transition-colors">
                                        再试一次
                                    </button>
                                </div>
                            ) : (
                                <div ref={solutionContainerRef} className="space-y-2 text-left bg-gray-50 p-6 rounded-b-lg border-x border-b border-gray-200 shadow-sm prose">
                                    <h3 className="font-bold text-gray-800 text-lg !mt-0">答案解析</h3>
                                    <div className="text-gray-800 text-base mt-2">
                                        <MathRenderer content={detailedSolution || '没有提供详细解析。'} />
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                </div>
                
                <div>
                    {renderAssessmentFooter()}
                    {renderTrainingFooter()}
                </div>
            </div>
        );
    }
    
    const renderBreakthroughFound = () => {
        const startTraining = () => {
            if (!questionForRetest) {
                setApiError("内部错误：无法开始训练。");
                setFlowState('ERROR');
                return;
            }
            initiateBreakthroughTraining(questionForRetest);
        };
        
        const exitAndSave = () => {
            if (!questionForRetest) return;
            onNavigate(ActivePage.AssessmentCenter, {
                breakthroughToSave: {
                    topicId: questionForRetest.topicId,
                    archetypeId: questionForRetest.archetypeId
                }
            });
        };

        if (!breakthroughArchetype) return null;

        return (
            <div className="w-full max-w-3xl mx-auto flex flex-col items-center justify-center h-full p-4 animate-fade-in-fast">
                <div className="bg-white p-8 rounded-2xl shadow-xl border border-gray-200 text-center">
                    <BeakerIcon className="w-20 h-20 text-blue-500 mx-auto mb-4" />
                    <h2 className="text-3xl font-bold text-gray-800">找到学习突破口！</h2>
                    <p className="text-gray-600 mt-2 text-lg">系统发现你在以下题型上需要加强训练：</p>

                    <div className="my-6 text-left bg-blue-50 p-6 rounded-lg border border-blue-200">
                        <p className="font-bold text-blue-800 text-lg">{breakthroughArchetype.archetypeType}</p>
                        <div className="text-gray-800 text-base mt-2 prose max-w-none">
                            <MathRenderer content={breakthroughArchetype.description} />
                        </div>
                    </div>
                    
                    <p className="text-gray-600 text-lg">准备好攻克它了吗？</p>

                    <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
                        <button onClick={exitAndSave} className="px-6 py-3 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 font-semibold order-2 sm:order-1">
                            稍后处理 (退出)
                        </button>
                        <button onClick={startTraining} className="inline-flex items-center justify-center px-8 py-3 rounded-lg bg-blue-600 text-white hover:bg-blue-700 font-bold shadow-lg transition transform hover:scale-105 order-1 sm:order-2">
                            开始突破训练
                            <ArrowRightIcon className="w-5 h-5 ml-2" />
                        </button>
                    </div>
                </div>
            </div>
        );
    };

    const renderContent = () => {
        switch(flowState) {
            case 'LOADING_INITIAL':
            case 'LOADING_DRILLDOWN':
                return <div className="flex items-center justify-center h-full"><ArrowPathIcon className="w-10 h-10 text-blue-600 animate-spin" /></div>;
            case 'ERROR':
                 return (
                    <div className="flex flex-col items-center justify-center min-h-full p-4 text-center">
                        <div className="w-full max-w-3xl p-8 bg-white rounded-lg shadow-xl">
                            <XCircleIcon className="w-20 h-20 text-red-500 mx-auto mb-4" />
                            <h2 className="text-3xl font-bold text-gray-800">发生错误</h2>
                            <p className="text-gray-600 mt-2 text-lg">{apiError || '发生未知错误，请稍后再试。'}</p>
                            <button onClick={() => onNavigate(ActivePage.AssessmentCenter)} className="mt-8 bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg shadow-md">
                                返回测评中心
                            </button>
                        </div>
                    </div>
                );
            case 'FINISHED':
                return (
                    <div className="flex flex-col items-center justify-center min-h-full p-4">
                        <div className="w-full max-w-3xl p-8 bg-white rounded-lg shadow-md text-center">
                            <CheckBadgeIcon className="w-20 h-20 text-green-500 mx-auto mb-4" />
                            <h2 className="text-3xl font-bold text-gray-800">恭喜你，练习完成！</h2>
                             <p className="text-gray-600 mt-2">{activeBreakthroughSession ? "你已成功攻克此薄弱点，太棒了！" : "你已掌握当前专题的所有内容，继续加油！"}</p>
                            <button onClick={() => onNavigate(ActivePage.AssessmentCenter, { clearBreakthrough: !!activeBreakthroughSession })} className="mt-8 bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg shadow-md transition transform hover:scale-105">
                                返回测评中心
                            </button>
                        </div>
                    </div>
                );
            case 'BREAKTHROUGH_FOUND':
                return renderBreakthroughFound();
            case 'ASSESSING_INITIAL':
            case 'ASSESSING_DRILLDOWN':
            case 'RETESTING':
            case 'CONSOLIDATING':
                 if (isAiProcessing || !processedQuestion) {
                     return (
                        <div className="flex items-center justify-center h-full text-center">
                            <div>
                                <ArrowPathIcon className="w-10 h-10 text-blue-600 animate-spin" />
                                <p className="mt-4 text-gray-600">AI 正在审查题目...</p>
                            </div>
                        </div>
                     );
                 }
                 return (
                    <div className="w-full max-w-5xl mx-auto h-full">
                        {renderQuestionArea()}
                    </div>
                 );
            default: return null;
        }
    }

    return (
        <div className="flex flex-col items-center p-4 sm:p-6 lg:p-8 min-h-full h-full">
            {!(flowState.startsWith('LOADING') || flowState === 'ERROR' || flowState === 'BREAKTHROUGH_FOUND' || isAiProcessing) && renderHeader()}
            
            <AskAiPopup state={selectionState} onAsk={handleAiQueryFromSelection} />
            {showFeedbackModal && <FeedbackModal onClose={() => setShowFeedbackModal(false)} />}
            {flowState === 'CONFIRM_EXIT' && <ConfirmationModal title="确认退出" message="确定要退出本次练习吗？进度将不会被保存。" confirmText="确认退出" onConfirm={onConfirmExit} onCancel={() => setFlowState(previousFlowState)} />}

            <div className="flex-grow w-full max-w-7xl mx-auto flex items-stretch justify-center relative">
                {renderContent()}
            </div>
        </div>
    );
};

export default TrainingPage;