import React, { useState } from 'react';
import * as api from '../services/apiService';

interface LoginPageProps {
    onLoginSuccess: () => void;
    onNavigateToRegister: () => void;
}

// Base64 encoded illustration to ensure it's always available without relying on file paths.
const illustrationBase64 = "data:image/png;base64,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-AEAcIA";

const LoginPage: React.FC<LoginPageProps> = ({ onLoginSuccess, onNavigateToRegister }) => {
    const [account, setAccount] = useState('');
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);
        try {
            const token = await api.login(account, password);
            localStorage.setItem('authToken', token);
            onLoginSuccess();
        } catch (err: any) {
            setError(err.message || '登录失败，请检查您的账号和密码。');
            setIsLoading(false);
        }
    };

    return (
        <div className="flex h-screen w-screen font-sans auth-background">
            {/* Left side: Illustration and Welcome */}
            <div className="hidden lg:flex w-3/5 flex-col items-start justify-center p-20">
                <div className="max-w-md">
                    <h1 style={{animationDelay: '100ms'}} className="text-5xl font-bold text-gray-800 leading-tight animate-character-reveal">Hey,</h1>
                    <h1 style={{animationDelay: '250ms'}} className="text-5xl font-bold text-gray-800 leading-tight animate-character-reveal">Welcome Back!</h1>
                </div>
                <img src={illustrationBase64} alt="Person studying at a desk" className="mt-12 w-full max-w-lg animate-fade-in-fast" style={{animationDelay: '400ms'}} />
            </div>

            {/* Right side: Login Form */}
            <div className="w-full lg:w-2/5 flex items-center justify-center p-8">
                <div className="w-full max-w-sm">
                    <div className="bg-white/40 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/40 animate-fade-in-fast" style={{animationDelay: '500ms'}}>
                        <div className="text-center mb-8">
                            <h2 className="text-3xl font-bold text-gray-800">登录</h2>
                            <p className="text-gray-500 mt-1">Welcome</p>
                        </div>

                        <form onSubmit={handleSubmit}>
                            <div className="space-y-6">
                                <div>
                                    <label htmlFor="account" className="block text-sm font-medium text-gray-700">账号</label>
                                    <input
                                        type="text"
                                        id="account"
                                        name="account"
                                        value={account}
                                        onChange={(e) => setAccount(e.target.value)}
                                        placeholder="请输入用户名或邮箱"
                                        className="mt-1 block w-full px-4 py-3 bg-white/70 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition"
                                        required
                                    />
                                </div>
                                <div>
                                    <label htmlFor="password"className="block text-sm font-medium text-gray-700">密码</label>
                                    <input
                                        type="password"
                                        id="password"
                                        name="password"
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        placeholder="请输入密码"
                                        className="mt-1 block w-full px-4 py-3 bg-white/70 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition"
                                        required
                                    />
                                </div>
                            </div>
                            
                            {error && <p className="text-sm text-red-600 mt-4 text-center">{error}</p>}

                            <div className="mt-8">
                                <button
                                    type="submit"
                                    disabled={isLoading}
                                    className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-lg font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition transform hover:scale-105 disabled:bg-gray-400 disabled:from-gray-400 disabled:to-gray-500 disabled:scale-100"
                                >
                                    {isLoading ? '登录中...' : '登录'}
                                </button>
                            </div>
                        </form>

                        <div className="mt-6 flex justify-between items-center text-sm">
                            <a href="#" onClick={(e) => { e.preventDefault(); onNavigateToRegister(); }} className="font-medium text-blue-600 hover:text-blue-500">
                                注册
                            </a>
                            <a href="#" className="font-medium text-gray-600 hover:text-gray-500">
                                忘记密码?
                            </a>
                        </div>
                        
                        <div className="mt-8 text-center">
                            <div className="inline-block p-2 bg-green-100 rounded-full cursor-pointer group">
                               <div className="w-8 h-8 bg-green-400 rounded-full animate-pulse-slow group-hover:animate-none transition"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LoginPage;