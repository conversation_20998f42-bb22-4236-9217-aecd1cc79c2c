


import React, { useState, useMemo, FC, useEffect } from 'react';
import { ActivePage, SubjectId, SubjectTopic, ProblemArchetype, TopicLevel, User } from '../types';
import { TRAINING_TOPICS } from '../constants';
import * as api from '../services/apiService';
import { ResponsiveContainer, ComposedChart, Bar, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { 
    ClockIcon, LightBulbIcon, ExclamationTriangleIcon, BoltIcon, ChevronLeftIcon, ChevronRightIcon, ChevronDownIcon, CalendarDaysIcon, FireIcon, PencilIcon, TrophyIcon, HandThumbUpIcon, AcademicCapIcon, ChatBubbleOvalLeftEllipsisIcon
} from '@heroicons/react/24/solid';
import ProgressBar from './ProgressBar';
import MathRenderer from './MarkdownKatex';

// --- MOCK DATA (retained for consistency) ---
const MOCK_DATA = {
  monthlyStats: {
    consecutiveCheckin: 15,
    totalCheckin: 245,
    totalHours: 44.5,
    totalProblems: 252,
    challengeProgress: 7,
    challengeGoal: 28,
  },
  learningTrends: {
    '30d': Array.from({ length: 30 }, (_, i) => ({
      date: `D${i + 1}`,
      '每日刷题量': 5 + Math.floor(Math.random() * 20) + (i > 15 ? i - 15 : 0),
      '综测正确率': Math.min(98, 65 + Math.floor(Math.random() * 10) + i * 0.5),
    })),
  },
  heatmapData: new Map(Array.from({length: 22}, (_, i) => {
      const day = i + 1;
      const hours = 0.5 + Math.random() * 4.5;
      const problems = Math.floor(5 + Math.random() * 20);
      return [`2025-06-${String(day).padStart(2,'0')}`, { hours, problems }];
  })),
  examOutlook: {
    name: "考研数学一模拟冲刺",
    countdown: 86400 * 14 + 3600 * 3 + 1800 + 42, // 14 days, 3 hours, 30 mins, 42 secs
    readinessIndex: 85,
    sprintAdvice: [
      "重点复习【中值定理】相关的辅助函数构造，这是你的薄弱环节。",
      "加强【递推数列】极限的计算练习，尝试多种方法求解。",
      "确保基础求导和积分公式的熟练度，避免在简单计算上失分。"
    ]
  }
};


// --- Helper Components & Functions ---
const Card: FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
    <div className={`bg-white p-6 rounded-2xl shadow-sm border border-gray-200 ${className}`}>{children}</div>
);

// --- Left Column Components (Refined) ---

const PersonalDashboardCard: FC = () => (
  <Card>
    <div className="grid grid-cols-2 gap-x-4 gap-y-6">
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-1">
          <FireIcon className="w-5 h-5 text-gray-400" />
          <p className="text-sm text-gray-500 font-medium">连续签到</p>
        </div>
        <p className="text-4xl font-bold text-gray-800">{MOCK_DATA.monthlyStats.consecutiveCheckin}</p>
      </div>
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-1">
          <CalendarDaysIcon className="w-5 h-5 text-gray-400" />
          <p className="text-sm text-gray-500 font-medium">累计签到</p>
        </div>
        <p className="text-4xl font-bold text-gray-800">{MOCK_DATA.monthlyStats.totalCheckin}</p>
      </div>
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-1">
          <ClockIcon className="w-5 h-5 text-gray-400" />
          <p className="text-sm text-gray-500 font-medium">本月总时长</p>
        </div>
        <p className="text-4xl font-bold text-gray-800">{MOCK_DATA.monthlyStats.totalHours.toFixed(1)}</p>
      </div>
      <div className="text-center">
        <div className="flex items-center justify-center gap-2 mb-1">
          <PencilIcon className="w-5 h-5 text-gray-400" />
          <p className="text-sm text-gray-500 font-medium">本月总刷题</p>
        </div>
        <p className="text-4xl font-bold text-gray-800">{MOCK_DATA.monthlyStats.totalProblems}</p>
      </div>
    </div>
  </Card>
);

const FreePassChallengeCard: FC = () => {
    const { challengeProgress, challengeGoal } = MOCK_DATA.monthlyStats;
    const progressPct = (challengeProgress / challengeGoal) * 100;
    return (
        <Card>
            <h3 className="font-bold text-gray-800 text-lg">免单挑战</h3>
            <div className="flex items-center gap-4 mt-3">
                <p className="text-sm text-gray-600 flex-shrink-0">
                    已完成 <span className="font-bold text-lg text-[#F97316]">{challengeProgress}</span> 天，你真棒！<br/>再坚持 <span className="font-bold text-lg text-[#F97316]">{challengeGoal - challengeProgress}</span> 天即可免单！
                </p>
                <div className="w-full bg-gray-200 rounded-full h-5 overflow-hidden">
                    <div className="bg-[#F97316] h-5 rounded-full transition-all duration-500" style={{ width: `${progressPct}%` }}></div>
                </div>
                <span className="text-lg font-bold text-[#F97316]">{challengeProgress}/{challengeGoal}</span>
            </div>
        </Card>
    );
};

const ExamOutlookCard: FC = () => {
    const [countdown, setCountdown] = useState(MOCK_DATA.examOutlook.countdown);
    
    useEffect(() => {
        const timer = setInterval(() => setCountdown(prev => (prev > 0 ? prev - 1 : 0)), 1000);
        return () => clearInterval(timer);
    }, []);

    const formatCountdown = (seconds: number) => {
        const d = Math.floor(seconds / (3600*24));
        const h = Math.floor(seconds % (3600*24) / 3600);
        const m = Math.floor(seconds % 3600 / 60);
        const s = seconds % 60;
        return `${String(d).padStart(2, '0')}天 ${String(h).padStart(2,'0')}:${String(m).padStart(2,'0')}:${String(s).padStart(2,'0')}`;
    }

    const readinessIndex = MOCK_DATA.examOutlook.readinessIndex;
    const circumference = 2 * Math.PI * 52;
    
    return (
        <Card>
            <h2 className="text-xl font-bold text-gray-800 flex items-center"><TrophyIcon className="w-6 h-6 mr-2 text-gray-500"/>下次挑战</h2>
            <div className="text-center md:text-left mt-4">
                 <div className="flex flex-col items-center justify-center gap-4">
                    <div className="relative w-40 h-40">
                         <svg className="w-full h-full" viewBox="0 0 120 120">
                            <circle cx="60" cy="60" r="52" fill="none" stroke="#E4E7ED" strokeWidth="10" />
                            <circle cx="60" cy="60" r="52" fill="none" stroke="#F97316" strokeWidth="10" strokeLinecap="round"
                                style={{ strokeDasharray: circumference, strokeDashoffset: circumference * (1 - readinessIndex / 100), transform: 'rotate(-90deg)', transformOrigin: 'center', transition: 'stroke-dashoffset 0.5s ease-out' }}
                            />
                        </svg>
                        <div className="absolute inset-0 flex flex-col items-center justify-center">
                            <span className="text-4xl font-bold text-gray-800">{readinessIndex}%</span>
                            <span className="text-xs text-gray-500 font-semibold">备考就绪指数</span>
                        </div>
                    </div>
                     <p className="font-semibold text-gray-700 text-lg">{MOCK_DATA.examOutlook.name}</p>
                    <p className="text-3xl font-mono font-bold text-gray-800 my-1">{formatCountdown(countdown)}</p>
                </div>
                <div className="mt-6">
                    <h4 className="font-semibold text-gray-700 mb-2">备考关键点:</h4>
                    <ul className="space-y-2 text-left leading-relaxed">
                        {MOCK_DATA.examOutlook.sprintAdvice.map((advice, i) => (
                            <li key={i} className="flex items-start text-sm">
                                <ExclamationTriangleIcon className="w-4 h-4 mr-2 mt-0.5 text-red-500 shrink-0" />
                                <span className="text-gray-700">{advice}</span>
                            </li>
                        ))}
                    </ul>
                </div>
                 <button className="w-full mt-6 bg-[#F97316] text-white font-bold rounded-xl shadow-lg hover:bg-orange-600 transition-all transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 flex items-center justify-center gap-2 py-4 text-lg">
                    <BoltIcon className="w-6 h-6" />
                    开启考前冲刺训练
                </button>
            </div>
        </Card>
    );
};


// --- Right Column Components (Reorganized into a single Tabbed Hub) ---

const MonthlyHeatmapLog: FC = () => {
    const [currentDate, setCurrentDate] = useState(new Date('2025-06-15'));
    
    const handlePrevMonth = () => setCurrentDate(d => new Date(d.getFullYear(), d.getMonth() - 1, 1));
    const handleNextMonth = () => setCurrentDate(d => new Date(d.getFullYear(), d.getMonth() + 1, 1));

    const calendarData = useMemo(() => {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();
        const firstDayOfMonth = new Date(year, month, 1);
        const lastDayOfMonth = new Date(year, month + 1, 0);
        const days = [];
        const startDayOfWeek = firstDayOfMonth.getDay() === 0 ? 6 : firstDayOfMonth.getDay() - 1; 
        for (let i = startDayOfWeek; i > 0; i--) days.push({ date: new Date(year, month, 1 - i), isCurrentMonth: false });
        for (let i = 1; i <= lastDayOfMonth.getDate(); i++) days.push({ date: new Date(year, month, i), isCurrentMonth: true });
        const endDayOfWeek = lastDayOfMonth.getDay() === 0 ? 6 : lastDayOfMonth.getDay() - 1;
        for (let i = 1; i < 7 - endDayOfWeek; i++) days.push({ date: new Date(year, month + 1, i), isCurrentMonth: false });
        return days;
    }, [currentDate]);
    
    const DayCell: FC<{ date: Date; isCurrentMonth: boolean }> = ({ date, isCurrentMonth }) => {
        const TARGET_HOURS = 3;
        const RADIUS = 14;
        const CIRCUMFERENCE = 2 * Math.PI * RADIUS;

        const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
        const stats = MOCK_DATA.heatmapData.get(dateStr);
        const hours = stats?.hours || 0;

        const progress = Math.min(1, hours / TARGET_HOURS);
        const strokeDashoffset = CIRCUMFERENCE * (1 - progress);
        
        const isToday = new Date().toDateString() === date.toDateString();
        const overTarget = hours > TARGET_HOURS;
        
        if (!isCurrentMonth) {
            return <div className="aspect-square"></div>;
        }

        const tooltipContent = stats ? `时长: ${stats.hours.toFixed(1)}h | 刷题: ${stats.problems}道` : '无记录';

        return (
            <div className="relative group aspect-square">
                <div className={`w-full h-full rounded-md bg-gray-50 flex items-center justify-center transition-all duration-200 ${isToday ? 'border-2 border-orange-500' : 'border border-gray-200'}`}>
                    <svg className="absolute w-full h-full" viewBox="0 0 36 36">
                        <circle cx="18" cy="18" r={RADIUS} fill="none" stroke="#e6e6e6" strokeWidth="3" />
                        <circle
                            cx="18" cy="18" r={RADIUS} fill="none" stroke="#4DABF7" strokeWidth="3" strokeLinecap="round"
                            strokeDasharray={CIRCUMFERENCE} strokeDashoffset={strokeDashoffset}
                            style={{ transform: 'rotate(-90deg)', transformOrigin: 'center', transition: 'stroke-dashoffset 0.5s ease-out' }}
                        />
                    </svg>
                    <span className={`relative text-xs font-semibold ${overTarget ? 'text-orange-500' : 'text-gray-700'}`}>
                        {date.getDate()}
                    </span>
                </div>
                 <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-max bg-gray-900 text-white text-xs rounded py-1 px-3 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-20">
                    {date.toLocaleDateString('zh-CN', {month: 'long', day: 'numeric'})}: {tooltipContent}
                     <svg className="absolute text-gray-900 h-2 w-full left-0 top-full" x="0px" y="0px" viewBox="0 0 255 255"><polygon className="fill-current" points="0,0 127.5,127.5 255,0"/></svg>
                </div>
            </div>
        );
    };

    return (
        <div className="p-1">
            <div className="flex justify-between items-center mb-4">
                 <h2 className="text-xl font-bold text-gray-800">月度学习日志</h2>
                 <div className="flex items-center bg-gray-100 rounded-lg p-1">
                    <button onClick={handlePrevMonth} className="p-2 rounded-md hover:bg-gray-200 text-gray-600"><ChevronLeftIcon className="w-5 h-5" /></button>
                    <span className="text-lg font-semibold text-gray-700 w-32 text-center">{currentDate.getFullYear()}年 {String(currentDate.getMonth() + 1).padStart(2, '0')}月</span>
                    <button onClick={handleNextMonth} className="p-2 rounded-md hover:bg-gray-200 text-gray-600"><ChevronRightIcon className="w-5 h-5" /></button>
                </div>
            </div>
            <div className="grid grid-cols-7 gap-1.5">
                {['一', '二', '三', '四', '五', '六', '日'].map(day => (
                    <div key={day} className="text-center font-semibold text-xs text-gray-500 pb-2">{day}</div>
                ))}
                {calendarData.map(({ date, isCurrentMonth }, index) => (
                    <DayCell key={index} date={date} isCurrentMonth={isCurrentMonth} />
                ))}
            </div>
        </div>
    );
};

const generateXiaoYuanDiagnosis = (topic: SubjectTopic, good: ProblemArchetype[], observation: ProblemArchetype[], review: ProblemArchetype[]): string => {
    let report = `二狗，关于 **${topic.name}** 这个专题，我仔细翻阅了你的每一次练习记录，形成了一份开篇诊断报告，希望能帮你精准定位，高效提升。\n\n`;

    report += `**你的闪光点 ✨**\n`;
    if (good.length > 0) {
        report += `首先必须给你点个大大的赞！在 **“${good[0].name}”** 这类问题上，你的表现堪称教科书级别，反应迅速，正确率极高。这充分说明你对 ${topic.name} 的核心定义和基本运算掌握得非常牢固，这是我们后续攻克难题的坚实基础。请继续保持！\n\n`;
    } else {
        report += `我们在这个专题的练习才刚刚开始，还没来得及完全展现你的实力。不过没关系，每一次尝试都是宝贵的积累，让我们一起稳步前进。\n\n`;
    }

    report += `**待发掘的“宝藏”——你的核心增长点 🧐**\n`;
    if (review.length > 0) {
        report += `我注意到，目前我们最大的挑战来自于 **“${review[0].name}”**。这类问题往往是综合性考察，比如它可能会要求你先用 **“${review[0].category}”** 的知识进行转化，再结合其他技巧求解。这不是说你基础不牢，而是我们在知识点的“串联应用”上还需加强。这恰恰是考研高分的关键，也是我们最有价值的提分点。\n\n`;
        if (observation.length > 0) {
            report += `另外，在 **“${observation[0].name}”** 这类技巧性较强的问题上，你的表现有些摇摆。这很正常！它说明你对这个技巧的理解已经到位，但还没能完全形成肌肉记忆。我们距离稳定掌握，只差“最后一公里”的练习量。\n\n`;
        }
    } else if (observation.length > 0) {
        report += `目前你的主要挑战集中在 **“${observation[0].name}”** 上。这类题目的特点是思路比较巧妙，需要一些解题经验的积累。好消息是，这部分一旦开窍，你的解题速度和准度都会有质的飞跃。我会重点为你推送这类题型。\n\n`;
    }
    else {
         report += `目前来看，你的各项能力发展非常均衡，没有发现特别的短板。这非常棒！但这并不意味着我们可以掉以轻心。“行百里者半九十”，我们接下来的目标是在保持现有水平的基础上，追求更优的解题方法和更快的解题速度，向满分发起冲击。\n\n`;
    }
    
    report += `**小圆的专属战略蓝图 🎯**\n`;
    report += `总结来说，你目前正处于**“基础坚实，蓄势待发”**的黄金阶段。我为你规划了接下来的三步走战略：\n`;
    report += `1.  **巩固长板，确保下限：** 继续保持基础题的训练，确保100%的正确率，这是我们得分的“基本盘”，也是自信的源泉。\n`;
    report += `2.  **专项爆破，提升上限：** 接下来，把主要精力集中在攻克那些“综合应用”和“解题技巧”类的问题上。别怕犯错，我会优先在“训练”模块为你推荐这些题型。每一次错误，都是一次精准定位的机会。\n`;
    report += `3.  **深化理解，举一反三：** 做完题后，不要急着做下一道。花5分钟复盘整个解题思路，问问自己：“这道题的核心考点是什么？”“我卡在了哪一步？”“有没有更简洁的思路？”。把一道题吃透，胜过盲目刷十道题。\n\n`;
    report += `记住，学习就像攀登，我们现在正仰望着更高处的风景。路途或许艰辛，但别担心，有我这个智能伴学陪你，我们一步一个脚印，山顶的无限风光，我们必能到达！`;
    
    return report;
};

const LevelProgressRow: FC<{ level: number; name: string; segments: any[]; progressText: string; }> = ({ level, name, segments, progressText }) => (
    <div className="flex items-center gap-3 p-2 bg-white rounded-lg border border-gray-200/80">
        <div className="font-semibold text-gray-700 w-24 shrink-0 text-sm">等级 {level}: {name}</div>
        <div className="flex-grow">
            <ProgressBar segments={segments} height="h-3" />
        </div>
        <div className="font-semibold text-gray-800 w-16 text-right shrink-0 text-sm">{progressText}</div>
    </div>
);


const ChapterMasteryOverview: FC<{ currentSubject: SubjectId; user: User }> = ({ currentSubject, user }) => {
    const [chaptersData, setChaptersData] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedChapterId, setSelectedChapterId] = useState<string | null>(null);

    useEffect(() => {
        const fetchMasteryData = async () => {
            setIsLoading(true);
            const topics = TRAINING_TOPICS[currentSubject] || [];
            const processedChapters = await Promise.all(
                topics
                    .filter(t => t.problemArchetypes && t.problemArchetypes.length > 0)
                    .map(async t => {
                        const good = t.problemArchetypes!.filter(a => a.masteryStatus === 'good_mastery');
                        const observation = t.problemArchetypes!.filter(a => a.masteryStatus === 'needs_observation');
                        const review = t.problemArchetypes!.filter(a => a.masteryStatus === 'needs_review');

                        const levelDefinitions: TopicLevel[] = t.levels && t.levels.length > 0 ? [...t.levels] : [
                            { level: 1, name: '基础知识', description: "", archetypeIds: t.problemArchetypes!.filter(a => a.category === '基础知识').map(a => a.id) },
                            { level: 2, name: '核心技巧', description: "", archetypeIds: t.problemArchetypes!.filter(a => a.category === '解题技巧').map(a => a.id) },
                            { level: 3, name: '综合应用', description: "", archetypeIds: t.problemArchetypes!.filter(a => a.category === '综合应用' || a.category === '复杂题型').map(a => a.id) },
                        ];
                        
                        const categoryMap: Record<number, string> = { 1: '基础知识', 2: '解题技巧', 3: '综合应用' };
                        
                        const levelProgress = await Promise.all(
                            levelDefinitions.slice(0, 3).map(async (levelInfo) => {
                                let masteredPct = 0;
                                const keyword = categoryMap[levelInfo.level];
                                if (keyword) {
                                    try {
                                        const percentageStr = await api.getProficiencyPercentage(user.id, keyword);
                                        masteredPct = parseFloat(percentageStr) || 0;
                                    } catch (e) {
                                        console.error(`Failed to fetch proficiency for ${keyword}`, e);
                                    }
                                }
                                
                                return {
                                    level: levelInfo.level,
                                    name: levelInfo.name,
                                    segments: [{ percentage: masteredPct, color: 'bg-blue-500' }],
                                    progressText: `${masteredPct.toFixed(1)}%`,
                                };
                            })
                        );
                        
                        const xiaoYuanDiagnosis = generateXiaoYuanDiagnosis(t, good, observation, review);

                        return {
                            id: t.id, name: t.name, subtitle: t.subtitle, levelProgress, xiaoYuanDiagnosis, 
                            totalArchetypes: t.problemArchetypes!.length,
                            good, observation, review,
                        };
                    })
            );
            setChaptersData(processedChapters);
            if (processedChapters.length > 0) {
                setSelectedChapterId(processedChapters[0].id);
            }
            setIsLoading(false);
        };

        if (user?.id) {
            fetchMasteryData();
        }
    }, [currentSubject, user]);

    
    if (isLoading) {
        return <div className="p-4 text-center text-gray-500">正在加载数据...</div>;
    }

    if (chaptersData.length === 0) return <p className="p-4 text-center text-gray-500">当前科目下暂无知识点数据。</p>;

    const currentChapter = chaptersData.find(c => c.id === selectedChapterId);

    return (
        <div className="flex flex-col lg:flex-row gap-6 p-1">
            {/* Left Nav and Progress */}
            <div className="lg:w-[45%] flex-shrink-0">
                <h3 className="font-bold text-gray-800 text-lg mb-3 px-2">我的知识版图</h3>
                <div className="space-y-2">
                    {chaptersData.map(chapter => {
                         const isSelected = selectedChapterId === chapter.id;
                         return (
                            <div key={chapter.id} className={`border rounded-lg transition-all duration-300 ${isSelected ? 'bg-blue-50 border-blue-200 shadow-sm' : 'border-transparent'}`}>
                                <button 
                                    onClick={() => setSelectedChapterId(isSelected ? null : chapter.id)} 
                                    className={`w-full text-left p-3 flex justify-between items-center rounded-lg ${!isSelected ? 'hover:bg-gray-100' : ''}`}
                                    aria-expanded={isSelected}
                                >
                                    <div>
                                        <p className={`font-semibold ${isSelected ? 'text-blue-700' : 'text-gray-800'}`}>{chapter.name}</p>
                                        {chapter.subtitle && <p className={`text-xs mt-0.5 ${isSelected ? 'text-blue-600' : 'text-gray-500'}`}>{chapter.subtitle}</p>}
                                    </div>
                                    <ChevronDownIcon className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${isSelected ? 'rotate-180' : ''}`} />
                                </button>
                                
                                {isSelected && (
                                    <div className="px-3 pb-3 mt-1 animate-fade-in-fast">
                                        <div className="space-y-2 border-t border-gray-200 pt-3">
                                            {chapter.levelProgress.map((levelData: any) => (
                                                <LevelProgressRow
                                                    key={levelData.level}
                                                    level={levelData.level}
                                                    name={levelData.name}
                                                    segments={levelData.segments}
                                                    progressText={levelData.progressText}
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>
                         )
                    })}
                </div>
            </div>

            {/* Right Details */}
            <div className="lg:w-[55%] flex-grow flex flex-col">
                {!currentChapter ? (
                    <div className="flex items-center justify-center h-full text-gray-500 bg-gray-50 rounded-lg p-10">
                        <p className="text-center text-lg">在左侧选择一个章节，<br/>查看小圆为你生成的专属诊断报告。</p>
                    </div>
                ) : (
                    <div className="animate-fade-in-fast h-full flex flex-col">
                        <div className="bg-[#FFFBEB] border-l-4 border-amber-400 p-6 rounded-r-lg shadow-sm h-full">
                            <h4 className="font-bold text-gray-900 flex items-center mb-4 text-xl">
                                <ChatBubbleOvalLeftEllipsisIcon className="w-8 h-8 mr-3 text-amber-600" /> 
                                <span>小圆的开篇诊断: <span className="text-amber-700">{currentChapter.name}</span></span>
                            </h4>
                            <div className="prose prose-base max-w-none text-gray-800 leading-relaxed">
                                <MathRenderer content={currentChapter.xiaoYuanDiagnosis} />
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};


const XiaoYuanReviewReport: FC = () => {
    const today = new Date();
    const isMonday = today.getDay() === 1; // 0 is Sunday, 1 is Monday

    const reportTitle = isMonday
        ? "小圆的每周战报 (本周一已更新)"
        : "小圆的每日复盘 (今日08:00已更新)";
    
    return (
        <div className="mt-8 pt-8 border-t border-gray-200">
            <h3 className="text-xl font-bold text-gray-800 flex items-center mb-4">
                <AcademicCapIcon className="w-7 h-7 mr-3 text-blue-600" />
                {reportTitle}
            </h3>
            
            <div className="space-y-6 text-gray-700 leading-relaxed">
                <p className="text-lg">
                    二狗，早上好。我们来系统地复盘一下你过去七天的学习成果。总体来看，上周是‘稳固提升’的一周，尤其是在高等数学的极限部分，进步非常明显。
                </p>

                <div>
                    <h4 className="font-bold text-gray-800 text-lg mb-3">关键发现</h4>
                    <div className="space-y-4">
                        <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <h5 className="font-semibold text-gray-900 flex items-center mb-2">
                                <HandThumbUpIcon className="w-5 h-5 mr-2 text-green-500" />
                                发现一：学习投入与回报率极高
                            </h5>
                            <p className="text-sm"><b className="text-gray-600">数据支撑：</b>趋势图显示，上周你的刷题量增加了20%，正确率也同步提升了12%。</p>
                            <p className="text-sm mt-1"><b className="text-gray-600">小圆解读：</b>这说明你的学习方法是高效的，你的每一份努力，都转化为了实实在在的分数。</p>
                        </div>
                        
                        <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                             <h5 className="font-semibold text-gray-900 flex items-center mb-2">
                                <ExclamationTriangleIcon className="w-5 h-5 mr-2 text-yellow-500" />
                                发现二：‘中值定理’已成为新的瓶颈
                            </h5>
                            <p className="text-sm"><b className="text-gray-600">数据支撑：</b>我回查了你的错题记录，上周所有错题中，有35%都与该知识点有关。</p>
                            <p className="text-sm mt-1"><b className="text-gray-600">小圆解读：</b>这是一个需要我们高度重视的信号。一旦我们攻克它，你的分数将迎来一次新的飞跃。</p>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 className="font-bold text-gray-800 text-lg mb-2">战略性建议</h4>
                    <p className="mb-2">综合以上分析，我给你的本周战略建议是：</p>
                    <ol className="list-decimal list-inside space-y-1 text-sm text-gray-800">
                        <li>保持当前的训练强度，你的方法是正确的。</li>
                        <li>在开始新的学习前，可以花些时间去‘训练’页面，系统会根据你的最新情况，优先为你推荐与‘中值定理’相关的题目。主动去解决它，效果会更好。</li>
                        <li>继续保持好心情，你做得非常出色。</li>
                    </ol>
                </div>
            </div>
        </div>
    );
};

const LearningTrendsTab: FC = () => {
    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-gray-800/90 text-white p-3 rounded-lg shadow-lg border border-gray-700">
                    <p className="font-bold mb-2">{label}</p>
                    <p style={{ color: '#1C7ED6' }}>
                        {`每日刷题量: ${payload[0].value}`}
                    </p>
                    <p style={{ color: '#F97316' }}>
                        {`综测正确率: ${payload[1].value}%`}
                    </p>
                </div>
            );
        }
        return null;
    };

    return (
        <div className="p-1">
            <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={MOCK_DATA.learningTrends['30d']} margin={{ right: 5, left: -15, top: 10, bottom: 5 }}>
                        <CartesianGrid stroke="#e5e7eb" strokeDasharray="3 3" vertical={false} />
                        <XAxis dataKey="date" tick={{ fill: '#6B7280', fontSize: 12 }} />
                        <YAxis yAxisId="left" stroke="#1C7ED6" tick={{ fill: '#1C7ED6', fontSize: 12 }} label={{ value: '题量', angle: -90, position: 'insideLeft', fill: '#1C7ED6' }} />
                        <YAxis yAxisId="right" orientation="right" stroke="#F97316" domain={[60, 100]} tickFormatter={(v) => `${v}%`} tick={{ fill: '#F97316', fontSize: 12 }} label={{ value: '正确率', angle: 90, position: 'insideRight', fill: '#F97316' }} />
                        <Tooltip content={<CustomTooltip />} cursor={{ stroke: '#F97316', strokeDasharray: '3 3' }}/>
                        <Legend />
                        <Bar yAxisId="left" dataKey="每日刷题量" fill="#1C7ED6" barSize={20} />
                        <Line yAxisId="right" type="monotone" dataKey="综测正确率" stroke="#F97316" strokeWidth={3} dot={{r: 4, strokeWidth: 2, fill: '#fff'}} activeDot={{r: 6}} />
                    </ComposedChart>
                </ResponsiveContainer>
            </div>
            <XiaoYuanReviewReport />
        </div>
    );
};

const LearningAnalysisHub: FC<{ currentSubject: SubjectId; user: User }> = ({ currentSubject, user }) => {
    const [activeTab, setActiveTab] = useState<'heatmap' | 'mastery' | 'trends'>('mastery');

    const tabs = [
        { id: 'heatmap', label: '月度学习日志' },
        { id: 'mastery', label: '章节诊断与分析' },
        { id: 'trends', label: '学习趋势图' },
    ];
    
    return (
        <Card>
            <div className="border-b border-gray-200 -mt-2 mb-4">
                <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                    {tabs.map(tab => (
                        <button key={tab.id} onClick={() => setActiveTab(tab.id as any)} className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${activeTab === tab.id ? 'border-blue-600 text-blue-700 font-semibold' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
                            {tab.label}
                        </button>
                    ))}
                </nav>
            </div>
            
            {activeTab === 'heatmap' && <MonthlyHeatmapLog />}
            {activeTab === 'mastery' && <ChapterMasteryOverview currentSubject={currentSubject} user={user} />}
            {activeTab === 'trends' && <LearningTrendsTab />}
        </Card>
    );
};


// --- Main Page Component ---
export const HistoryPage: React.FC<{ currentSubject: SubjectId, user: User, onNavigate: (page: ActivePage, context?: any) => void }> = ({ currentSubject, user, onNavigate }) => {
    return (
        <div className="w-full min-h-full p-6">
            <style>{`
                .custom-scrollbar::-webkit-scrollbar {
                    width: 6px;
                }
                .custom-scrollbar::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 10px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb {
                    background: #ccc;
                    border-radius: 10px;
                }
                .custom-scrollbar::-webkit-scrollbar-thumb:hover {
                    background: #aaa;
                }
            `}</style>
            <div className="max-w-screen-2xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6 items-start">
                {/* Left Column */}
                <div className="lg:col-span-1 space-y-6 sticky top-6">
                    <PersonalDashboardCard />
                    <FreePassChallengeCard />
                    <ExamOutlookCard />
                </div>
                {/* Right Column */}
                <div className="lg:col-span-2 space-y-6">
                    <LearningAnalysisHub currentSubject={currentSubject} user={user} />
                </div>
            </div>
        </div>
    );
};