import React, { useState, useMemo } from 'react';
import { SubjectId, SubjectTopic, ProblemArchetype, Question } from '../types';
import { FolderIcon, ChevronDownIcon } from '@heroicons/react/24/solid';
import MathRenderer from './MarkdownKatex';

interface DataCenterPageProps {
  currentSubject: SubjectId;
  selectedTopic?: SubjectTopic | null;
}

const QuestionPreview: React.FC<{ question: Question }> = ({ question }) => (
    <div className="text-sm text-gray-800 py-3 px-4 border-l-2 border-gray-300 ml-4 bg-white/60 rounded-r-md">
        <MathRenderer content={question.questionText} />
    </div>
);

const MasteryItem: React.FC<{ archetype: ProblemArchetype }> = ({ archetype }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    
    const relatedQuestions: Question[] = useMemo(() => 
        [], // MOCK_QUESTIONS was removed, so we return an empty array.
        [archetype.id]
    );

    return (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm mb-3 transition-shadow hover:shadow-md">
            <div 
                className="flex items-center justify-between p-4 cursor-pointer"
                onClick={() => setIsExpanded(!isExpanded)}
                aria-expanded={isExpanded}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && setIsExpanded(!isExpanded)}
            >
                <div className="flex items-center min-w-0">
                    <FolderIcon className="w-7 h-7 text-gray-400 mr-4 shrink-0" />
                    <div className="min-w-0">
                        <span className="font-semibold text-gray-800 text-base leading-tight truncate">[{archetype.category}] {archetype.name}</span>
                    </div>
                </div>
                <ChevronDownIcon className={`w-5 h-5 text-gray-500 transition-transform transform duration-200 shrink-0 ml-4 ${isExpanded ? 'rotate-180' : ''}`} />
            </div>
            {isExpanded && (
                <div className="px-4 pb-4 border-t border-gray-200 bg-gray-50/70">
                    <div className="prose prose-sm max-w-none text-gray-600 my-3 px-2">
                         <MathRenderer content={archetype.description} />
                    </div>
                    <div className="space-y-2 mt-4">
                        {relatedQuestions.length > 0 ? (
                            relatedQuestions.map(q => <QuestionPreview key={q.id} question={q} />)
                        ) : (
                            <p className="text-sm text-gray-500 py-2 px-2">暂无相关题目示例。</p>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

const DataCenterPage: React.FC<DataCenterPageProps> = ({ currentSubject, selectedTopic }) => {

  const categorizedArchetypes = useMemo(() => {
    const archetypes = selectedTopic?.problemArchetypes || [];
    const needsReview: ProblemArchetype[] = [];
    const needsObservation: ProblemArchetype[] = [];
    const goodMastery: ProblemArchetype[] = [];

    archetypes.forEach(arch => {
      switch (arch.masteryStatus) {
        case 'needs_review':
          needsReview.push(arch);
          break;
        case 'needs_observation':
          needsObservation.push(arch);
          break;
        case 'good_mastery':
          goodMastery.push(arch);
          break;
        default:
          needsObservation.push(arch);
      }
    });

    return { needsReview, needsObservation, goodMastery };
  }, [selectedTopic]);

  if (!selectedTopic) {
    return <div className="p-8 text-center text-gray-500">请先在“训练”标签页选择一个专项。</div>
  }

  const hasContent = categorizedArchetypes.needsReview.length > 0 ||
                     categorizedArchetypes.needsObservation.length > 0 ||
                     categorizedArchetypes.goodMastery.length > 0;

  return (
    <div className="p-4 md:p-0">
        {!hasContent && (
            <div className="text-center py-16 text-gray-500 bg-white rounded-lg shadow-sm">
                <h3 className="text-xl font-semibold">暂无历史记录</h3>
                <p className="mt-2">完成一些训练后，这里会展示你的学习历史和掌握情况。</p>
            </div>
        )}

        {categorizedArchetypes.needsReview.length > 0 && (
            <div className="mb-12">
                <div className="text-center my-6">
                    <h2 className="text-xl font-bold text-orange-500">
                        需要复习 (会随机抽查)
                    </h2>
                    <div className="w-24 h-1 bg-orange-400 mx-auto mt-2 rounded-full"></div>
                </div>
                <div className="space-y-3">
                    {categorizedArchetypes.needsReview.map(archetype => (
                        <MasteryItem key={archetype.id} archetype={archetype} />
                    ))}
                </div>
            </div>
        )}

        {categorizedArchetypes.needsObservation.length > 0 && (
            <div className="mb-12">
                <h3 className="text-xl font-semibold text-gray-700 mb-4 pb-2 border-b-2 border-gray-200">有待巩固</h3>
                <div className="space-y-3">
                    {categorizedArchetypes.needsObservation.map(archetype => (
                        <MasteryItem key={archetype.id} archetype={archetype} />
                    ))}
                </div>
            </div>
        )}
        
        {categorizedArchetypes.goodMastery.length > 0 && (
            <div className="mb-8">
                <h3 className="text-xl font-semibold text-gray-700 mb-4 pb-2 border-b-2 border-gray-200">掌握良好</h3>
                <div className="space-y-3">
                    {categorizedArchetypes.goodMastery.map(archetype => (
                        <MasteryItem key={archetype.id} archetype={archetype} />
                    ))}
                </div>
            </div>
        )}
    </div>
  );
};

export default DataCenterPage;