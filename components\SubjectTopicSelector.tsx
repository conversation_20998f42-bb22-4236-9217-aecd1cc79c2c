
import React from 'react';
import { SubjectTopic } from '../types';
import ChevronDownIcon from './icons/ChevronDownIcon';

interface SubjectTopicSelectorProps {
  topics: SubjectTopic[];
  selectedTopic: SubjectTopic | null;
  onSelectTopic: (topic: SubjectTopic) => void;
  label?: string;
  disabled?: boolean;
}

const SubjectTopicSelector: React.FC<SubjectTopicSelectorProps> = ({ 
  topics, 
  selectedTopic, 
  onSelectTopic,
  label = "选择测试科目",
  disabled = false 
}) => {
  return (
    <div className="my-4">
      <label htmlFor="topic-select" className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
      <div className="relative">
        <select
          id="topic-select"
          name="topic-select"
          className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-blue focus:border-primary-blue sm:text-sm rounded-md appearance-none shadow-sm disabled:bg-gray-100 disabled:cursor-not-allowed"
          value={selectedTopic ? selectedTopic.id : ""}
          onChange={(e) => {
            if (disabled) return;
            const topic = topics.find(t => t.id === e.target.value);
            if (topic) onSelectTopic(topic);
          }}
          disabled={disabled}
        >
          <option value="" disabled>-- {label} --</option>
          {topics.map(topic => (
            <option key={topic.id} value={topic.id}>{topic.name}</option>
          ))}
        </select>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
          <ChevronDownIcon className="h-4 w-4" />
        </div>
      </div>
    </div>
  );
};

export default SubjectTopicSelector;