import React, { useState, useRef, useEffect } from 'react';
import ChevronDownIcon from './icons/ChevronDownIcon';

interface DropdownItem {
  label: string;
  onClick: () => void;
}

interface DropdownProps {
  trigger: React.ReactElement<React.HTMLAttributes<HTMLElement>>; // Expect an HTML-like element
  items: DropdownItem[];
  dropdownAlign?: 'left' | 'right';
  onDropdownToggle?: (isOpen: boolean) => void; // Callback for when dropdown opens/closes
}

const Dropdown: React.FC<DropdownProps> = ({ trigger, items, dropdownAlign = 'left', onDropdownToggle }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleDropdown = () => {
    const newState = !isOpen;
    setIsOpen(newState);
    if (onDropdownToggle) {
      onDropdownToggle(newState);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        if (isOpen) {
            setIsOpen(false);
            if (onDropdownToggle) {
                onDropdownToggle(false);
            }
        }
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onDropdownToggle]);

  // Clone the trigger to add dropdown-specific onClick behavior and ARIA attributes.
  // This preserves the trigger's original onClick and other props.
  const EnhancedTrigger = React.cloneElement(
    trigger,
    {
      onClick: (e: React.MouseEvent<HTMLElement>) => {
        // Execute the trigger's original onClick if it was provided
        if (trigger.props && typeof trigger.props.onClick === 'function') {
          trigger.props.onClick(e);
        }
        // Then, toggle the dropdown
        toggleDropdown();
      },
      'aria-haspopup': 'true',
      'aria-expanded': isOpen,
    }
  );

  return (
    <div className="relative inline-block text-left" ref={dropdownRef}>
      {EnhancedTrigger}
      {isOpen && (
        <div
          className={`origin-top-${dropdownAlign} absolute ${dropdownAlign}-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50`}
        >
          <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
            {items.map((item, index) => (
              <a
                key={index}
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  item.onClick();
                  setIsOpen(false);
                  if (onDropdownToggle) onDropdownToggle(false);
                }}
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                role="menuitem"
              >
                {item.label}
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Dropdown;