




import React, { useState, useEffect, useMemo, FC } from 'react';
import { TrophyIcon, ClockIcon, CheckBadgeIcon, UserGroupIcon, ArrowRightIcon, BookOpenIcon, XMarkIcon, ExclamationTriangleIcon, ShieldCheckIcon, CalendarDaysIcon, ChartBarIcon, SparklesIcon, CheckCircleIcon, XCircleIcon, ChevronDownIcon, ChatBubbleOvalLeftEllipsisIcon } from '@heroicons/react/24/solid';
import { ComposedChart, BarChart, Bar, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, ReferenceLine } from 'recharts';
import MathRenderer from './MarkdownKatex';
import { ActivePage } from '../types';
import { MOCK_WEEKLY_EXAM } from '../constants';


// --- MOCK DATA & CONFIG ---
const TEST_DAY = 1; // Monday (0=Sun, 1=Mon, ...)
const TEST_START_HOUR = 18; // 18:00
const TEST_END_HOUR = 24; // End of day

const MOCK_PAST_EXAMS = [
    { 
        id: 1, 
        type: '月考', 
        name: "七月月度摸底测试", 
        date: "2025-07-26", 
        score: 92,
        rank: 98,
        totalParticipants: 21540,
        quartiles: { q1: 75, median: 84, q3: 91 },
        fullReport: {
            aiAnalysis: `吴二狗同学，你的**七月月度摸底测试**表现非常出色！92分的成绩和全站前0.5%的排名证明了你强大的实力，为你喝彩！\n\n**亮点发掘：**\n*   **极限与导数：** 这部分是你绝对的强项，所有相关题目全部正确，显示出你对洛必达法则、泰勒公式和导数计算的深刻理解。\n*   **解题速度：** 你提前了25分钟完成考试，说明你的基础非常扎实，解题效率很高。\n\n**待改进突破口：**\n*   本次失分主要集中在定积分的应用，特别是**利用定积分求旋转体体积**的题目。这说明你对定积分的几何意义和相关公式的应用还需加强。\n\n**后续建议：**\n1.  **专项突破：** 接下来，请在“训练”模块中，重点练习“定积分应用”专题，特别是关于旋转体体积和平面图形面积的计算。\n2.  **巩固错题：** 仔细回顾本次的错题解析，确保理解每一个步骤，争取下次不再犯类似错误。\n\n你已经走在了很多人的前面，继续保持，考研必胜！`,
            allQuestions: [
                { id: 'new_q_1', questionText: '求极限 $\\lim\\limits_{x \\to 0} \\frac{e^x - e^{-x} - 2x}{x - \\sin x}$ 的值。', options: ['A. 1', 'B. 2', 'C. -1', 'D. -2'], userAnswer: 'B', standardAnswer: 'B', isCorrect: true, detailedSolution: '此为 $\\frac{0}{0}$ 型未定式，可连续使用三次洛必达法则，最终得到 $\\lim\\limits_{x \\to 0} \\frac{e^x + e^{-x}}{\\cos x} = \\frac{1+1}{1} = 2$。' },
                { id: 'new_q_9', questionText: '计算定积分 $\\int_0^{\\pi} \\sin^2 x dx$。', options: [], userAnswer: 'pi/2', standardAnswer: '$\\pi/2$', isCorrect: true, detailedSolution: '利用降幂公式 $\\sin^2 x = \\frac{1 - \\cos(2x)}{2}$，积分可得 $\\frac{1}{2} [x - \\frac{1}{2}\\sin(2x)]_0^{\\pi} = \\frac{\\pi}{2}$。' },
                { id: 'new_q_volume', questionText: '求由曲线 $y=\\sqrt{x}$，直线 $x=1$ 及 $x$ 轴所围成的图形绕 $x$ 轴旋转一周所得旋转体的体积。', options: [], userAnswer: 'pi', standardAnswer: '$\\pi/2$', isCorrect: false, detailedSolution: '旋转体体积公式为 $V = \\int_a^b \\pi [f(x)]^2 dx$。此题中，$f(x)=\\sqrt{x}$，$a=0, b=1$。$V = \\int_0^1 \\pi (\\sqrt{x})^2 dx = \\pi \\int_0^1 x dx = \\pi [\\frac{1}{2}x^2]_0^1 = \\frac{\\pi}{2}$。' },
            ],
            scoreDistribution: Array.from({length: 20}, (_, i) => ({ score: 5 * i, count: Math.round(50 + Math.random() * 800 + (i > 10 ? (20-i) * 100 : i*50)) }))
        }
    },
    { 
        id: 2, 
        type: '周测', 
        name: "七月第三周周测", 
        date: "2025-07-19", 
        score: 85,
        rank: 210,
        totalParticipants: 3105,
        quartiles: { q1: 70, median: 80, q3: 88 },
    },
    { 
        id: 3, 
        type: '周测', 
        name: "七月第二周周测", 
        date: "2025-07-12", 
        score: 78,
        rank: 950,
        totalParticipants: 2980,
        quartiles: { q1: 68, median: 77, q3: 85 },
    },
];

// --- MOCK DATA FOR CURRENT MONTH'S TESTS ---
const CURRENT_MONTH_TESTS = [
    { id: 'w1', type: '周测', name: '六月第一周周测', dates: '6.03 - 6.09', status: 'COMPLETED' },
    { id: 'w2', type: '周测', name: '六月第二周周测', dates: '6.10 - 6.16', status: 'UPCOMING' },
    { id: 'w3', type: '周测', name: '六月第三周周测', dates: '6.17 - 6.23', status: 'LOCKED' },
    { id: 'w4', type: '月考', name: '六月月度综合测试', dates: '6.24 - 6.30', status: 'LOCKED' }
];

// --- Sub-components for Modal ---
const ExamReviewReport: FC<{ report: any }> = ({ report }) => (
    <div className="p-6 bg-blue-50/50 rounded-lg border-l-4 border-blue-400">
        <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <ChatBubbleOvalLeftEllipsisIcon className="w-7 h-7 mr-3 text-blue-600" />
            AI智能复盘
        </h3>
        <div className="prose prose-base max-w-none text-gray-700 leading-relaxed">
            <MathRenderer content={report.aiAnalysis} />
        </div>
    </div>
);

const QuestionItem: FC<{ question: any }> = ({ question }) => {
    const [isSolutionVisible, setIsSolutionVisible] = useState(false);
    return (
        <li className={`p-4 rounded-lg shadow-sm border ${question.isCorrect ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
            <div className="flex justify-between items-start">
                <div className="prose prose-sm max-w-none mr-4">
                    <MathRenderer content={question.questionText} />
                </div>
                {question.isCorrect 
                    ? <CheckCircleIcon className="w-8 h-8 text-green-500 shrink-0" /> 
                    : <XCircleIcon className="w-8 h-8 text-red-500 shrink-0" />}
            </div>
            <div className="mt-3 text-sm">
                <p><b>你的答案:</b> {question.userAnswer}</p>
                {!question.isCorrect && <p><b>正确答案:</b> <MathRenderer content={question.standardAnswer} className="inline-block" /></p>}
            </div>
            {question.detailedSolution && (
                 <div className="mt-3">
                    <button onClick={() => setIsSolutionVisible(!isSolutionVisible)} className="text-sm font-semibold text-blue-600 hover:underline flex items-center">
                        {isSolutionVisible ? '收起解析' : '查看解析'}
                        <ChevronDownIcon className={`w-4 h-4 ml-1 transition-transform ${isSolutionVisible ? 'rotate-180' : ''}`} />
                    </button>
                    {isSolutionVisible && (
                        <div className="mt-2 p-3 bg-white rounded border border-gray-200 prose prose-sm max-w-none animate-fade-in-fast">
                             <MathRenderer content={question.detailedSolution} />
                        </div>
                    )}
                </div>
            )}
        </li>
    );
};

const QuestionList: FC<{ report: any }> = ({ report }) => (
    <div>
        <h3 className="text-xl font-bold text-gray-800 mb-4">试卷详情</h3>
        <ul className="space-y-4">
            {report.allQuestions.map((q: any) => <QuestionItem key={q.id} question={q} />)}
        </ul>
    </div>
);

const ScoreDistributionChart: FC<{ report: any, userScore: number }> = ({ report, userScore }) => (
    <div>
         <h3 className="text-xl font-bold text-gray-800 mb-4">分数分布</h3>
         <p className="text-sm text-gray-600 mb-4">
            本次考试的分数分布情况如下，竖直的虚线代表你的得分位置。
         </p>
         <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
                <BarChart data={report.scoreDistribution} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="score" name="分数段" unit="分" />
                    <YAxis name="人数" />
                    <Tooltip 
                         cursor={{fill: 'rgba(239, 246, 255, 0.5)'}}
                         contentStyle={{ background: 'rgba(255, 255, 255, 0.9)', border: '1px solid #ddd', borderRadius: '8px' }}
                    />
                    <Bar dataKey="count" fill="#818cf8" name="人数" />
                    <ReferenceLine yAxisId={0} x={userScore} stroke="#ef4444" strokeDasharray="4 4" strokeWidth={2} label={{ value: `我的分数: ${userScore}`, position: 'insideTopRight', fill: '#ef4444' }} />
                </BarChart>
            </ResponsiveContainer>
         </div>
    </div>
);

const ExamDetailsModal: FC<{ exam: any, onClose: () => void }> = ({ exam, onClose }) => {
    const [activeTab, setActiveTab] = useState('review');
    const hasFullReport = !!exam.fullReport;

    const tabs = [
        { id: 'review', label: 'AI智能复盘' },
        { id: 'questions', label: '试卷详情' },
        { id: 'data', label: '数据分析' },
    ];

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4" onClick={onClose}>
            <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col transform transition-all animate-fade-in-fast" onClick={e => e.stopPropagation()}>
                <div className="p-5 border-b border-gray-200 flex justify-between items-center shrink-0">
                    <div>
                        <h3 className="font-bold text-xl text-gray-900">{exam.name} - 详细报告</h3>
                        <p className="text-sm text-gray-500">{exam.date} | 得分: {exam.score}</p>
                    </div>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600 p-1 rounded-full"><XMarkIcon className="w-6 h-6" /></button>
                </div>

                {hasFullReport ? (
                    <>
                        <div className="px-6 border-b border-gray-200 shrink-0">
                            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                                {tabs.map(tab => (
                                    <button key={tab.id} onClick={() => setActiveTab(tab.id)} className={`whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'}`}>
                                        {tab.label}
                                    </button>
                                ))}
                            </nav>
                        </div>
                        <div className="p-6 flex-grow overflow-y-auto">
                            {activeTab === 'review' && <ExamReviewReport report={exam.fullReport} />}
                            {activeTab === 'questions' && <QuestionList report={exam.fullReport} />}
                            {activeTab === 'data' && <ScoreDistributionChart report={exam.fullReport} userScore={exam.score} />}
                        </div>
                    </>
                ) : (
                    <div className="p-6 flex-grow overflow-y-auto text-center text-gray-500">
                        <ExclamationTriangleIcon className="w-12 h-12 mx-auto text-yellow-400 mb-4" />
                        <p>本次考试的详细AI报告正在生成中，请稍后再试。</p>
                        <p className="text-sm mt-1">目前仅支持查看部分近期考试的完整报告。</p>
                    </div>
                )}
                 <div className="p-4 bg-gray-50 rounded-b-2xl text-right border-t border-gray-200 shrink-0">
                    <button onClick={onClose} className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-8 rounded-lg shadow-sm transition transform hover:scale-105">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    );
};

// --- Helper Components & Functions ---
const Card: FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => (
    <div className={`bg-white p-6 rounded-xl shadow-sm border border-gray-200 ${className}`}>{children}</div>
);

const getRankBadge = (rank: number, totalParticipants: number) => {
    const percentage = (rank / totalParticipants) * 100;
    let badge = null;
    if (percentage <= 1) badge = { text: '顶尖 1%', color: 'text-amber-800 bg-gradient-to-br from-yellow-300 to-amber-400 border-yellow-500 shadow-md' };
    else if (percentage <= 10) badge = { text: '前 10%', color: 'text-gray-800 bg-gradient-to-br from-gray-200 to-gray-300 border-gray-400' };
    else if (percentage <= 25) badge = { text: '前 25%', color: 'text-orange-900 bg-gradient-to-br from-orange-300 to-amber-500 border-amber-600' };
    else if (percentage <= 50) badge = { text: '前 50%', color: 'text-blue-800 bg-blue-100 border-blue-400' };
    if (!badge) return null;
    return <div className={`flex items-center gap-1.5 text-xs font-bold px-2.5 py-1 rounded-full border ${badge.color}`}><ShieldCheckIcon className="w-4 h-4" /><span>{badge.text}</span></div>;
};

const PastExamsCard: FC<{ exams: any[], onViewDetails: (exam: any) => void }> = ({ exams, onViewDetails }) => (
    <Card>
        <h3 className="text-xl font-bold text-gray-800 flex items-center mb-4"><BookOpenIcon className="w-6 h-6 mr-2 text-green-600"/>个人考试回顾</h3>
        <ul role="list" className="-my-4 divide-y divide-gray-200">
        {exams.map(exam => (
            <li key={exam.id} className="py-4 flex items-center space-x-4">
                <div className={`w-12 h-12 flex items-center justify-center rounded-lg shrink-0 ${exam.type === '月考' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'}`}><TrophyIcon className="w-7 h-7"/></div>
                <div className="flex-1 min-w-0">
                    <p className="text-md font-medium text-gray-900 truncate">{exam.name}</p>
                    <p className="text-sm text-gray-500 truncate">{exam.date}</p>
                </div>
                <div className="text-right shrink-0">
                    <div className="flex items-center justify-end gap-2">{getRankBadge(exam.rank, exam.totalParticipants)}<p className="inline-flex items-center text-base font-semibold text-gray-900">{exam.score}分</p></div>
                    <p className="text-xs text-gray-500 mt-0.5">排名: {exam.rank}/{exam.totalParticipants}</p>
                </div>
                <button onClick={() => onViewDetails(exam)} className="bg-gray-100 text-gray-700 font-semibold py-1.5 px-4 rounded-full text-sm hover:bg-gray-200 transition-colors ml-4">查看详情</button>
            </li>
        ))}
        </ul>
    </Card>
);

const PerformanceTrendChart: FC<{ pastExams: typeof MOCK_PAST_EXAMS }> = ({ pastExams }) => {
    const chartData = useMemo(() => {
        return [...pastExams].reverse().map(exam => ({
            name: `${exam.date.slice(5)} ${exam.type}`,
            '我的分数': exam.score,
            '我的排名': exam.rank,
            '参与人数': exam.totalParticipants,
            quartiles: exam.quartiles,
        }));
    }, [pastExams]);

    const maxRank = useMemo(() => Math.max(...chartData.map(d => d['我的排名']), 0), [chartData]);

    return (
        <Card>
            <h3 className="text-xl font-bold text-gray-800 mb-1">历次考试成绩与排名趋势</h3>
            <p className="text-sm text-gray-500 mb-6">回顾你在每次考试中的表现，追踪你的进步轨迹。</p>
            <div className="h-96">
                <ResponsiveContainer width="100%" height="100%">
                    <ComposedChart data={chartData} margin={{ top: 5, right: 20, left: 0, bottom: 25 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
                        <XAxis dataKey="name" tick={{ fill: '#6B7280', fontSize: 12 }} angle={-25} textAnchor="end" height={60} interval={0} />
                        <YAxis yAxisId="score" orientation="left" stroke="#F97316" domain={[0, 100]} label={{ value: '分数', angle: -90, position: 'insideLeft', fill: '#F97316' }} />
                        <YAxis yAxisId="rank" orientation="right" stroke="#8B5CF6" reversed={true} domain={[0, maxRank > 0 ? maxRank * 1.1 : 100]} label={{ value: '排名 (越靠上越好)', angle: 90, position: 'insideRight', fill: '#8B5CF6' }} />
                        <Tooltip contentStyle={{ background: 'rgba(255, 255, 255, 0.9)', border: '1px solid #ddd', borderRadius: '8px', boxShadow: '0 2px 10px rgba(0,0,0,0.1)' }} labelStyle={{ fontWeight: 'bold', color: '#333' }}
                            formatter={(value: any, name: any, props: any) => {
                                if (name === '我的排名') return [value, `排名 (共${props.payload['参与人数']}人)`];
                                if (Array.isArray(value)) return [`${value[0]} - ${value[1]}`, name];
                                return [value, name];
                            }}
                        />
                        <Legend wrapperStyle={{ paddingTop: '40px' }} />
                        <Bar yAxisId="score" dataKey={d => [d.quartiles?.q1, d.quartiles?.q3]} fill="#818cf8" fillOpacity={0.3} barSize={30} name="分数区间(25%-75%)" />
                        <Line yAxisId="score" type="monotone" dataKey="quartiles.median" stroke="#a78bfa" strokeWidth={2} name="中位分数" strokeDasharray="3 3" dot={false} />
                        <Line yAxisId="score" type="monotone" dataKey="我的分数" stroke="#F97316" strokeWidth={3} name="我的分数" dot={{ r: 5, strokeWidth: 2 }} activeDot={{ r: 8 }} />
                        <Line yAxisId="rank" type="monotone" dataKey="我的排名" stroke="#8B5CF6" strokeWidth={2} name="我的排名" strokeDasharray="5 5" dot={{ r: 5 }} />
                    </ComposedChart>
                </ResponsiveContainer>
            </div>
        </Card>
    );
};

const MonthlyTestCalendar: FC<{ onStartExam: (examId: string) => void }> = ({ onStartExam }) => {
    return (
        <Card>
             <h3 className="text-xl font-bold text-gray-800 mb-4">本月挑战</h3>
             <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {CURRENT_MONTH_TESTS.map(test => {
                    const isMonthly = test.type === '月考';
                    const isCompleted = test.status === 'COMPLETED';
                    const isLocked = test.status === 'LOCKED';
                    const isUpcoming = test.status === 'UPCOMING';

                    return (
                        <div key={test.id} className={`p-4 rounded-lg border-2 flex flex-col justify-between ${isMonthly ? 'bg-blue-50 border-blue-200' : 'bg-green-50 border-green-200'}`}>
                            <div>
                                <p className={`font-bold ${isMonthly ? 'text-blue-800' : 'text-green-800'}`}>{test.name}</p>
                                <p className="text-xs text-gray-500">{test.dates}</p>
                            </div>
                            <div className="mt-4">
                                {isCompleted ? (
                                    <button disabled className="w-full text-sm font-semibold py-2 px-3 rounded-md bg-white border border-gray-300 text-gray-500 flex items-center justify-center gap-1.5"><CheckCircleIcon className="w-5 h-5"/>已完成</button>
                                ) : isUpcoming ? (
                                    <button onClick={() => onStartExam(test.id)} className="w-full text-sm font-semibold py-2 px-3 rounded-md bg-blue-600 text-white hover:bg-blue-700 shadow-sm flex items-center justify-center gap-1.5 animate-pulse-slow">即将开始<ArrowRightIcon className="w-4 h-4" /></button>
                                ) : (
                                     <button disabled className="w-full text-sm font-semibold py-2 px-3 rounded-md bg-gray-200 text-gray-500 cursor-not-allowed flex items-center justify-center gap-1.5"><ClockIcon className="w-4 h-4"/>已锁定</button>
                                )}
                            </div>
                        </div>
                    );
                })}
             </div>
        </Card>
    )
}

const CompetitionPage: React.FC<{ onNavigate: (page: ActivePage, context?: any) => void }> = ({ onNavigate }) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [selectedExam, setSelectedExam] = useState<any>(null);

    const handleViewDetails = (exam: any) => {
        setSelectedExam(exam);
        setIsModalOpen(true);
    };
    
    const handleStartExam = (examId: string) => {
        if (examId === 'w2') {
            onNavigate(ActivePage.ExamTaking, { examSession: MOCK_WEEKLY_EXAM });
        }
    };


    return (
        <div className="min-h-full p-4 md:p-6 lg:p-8">
            {isModalOpen && <ExamDetailsModal exam={selectedExam} onClose={() => setIsModalOpen(false)} />}
            
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-800 flex items-center"><TrophyIcon className="w-8 h-8 mr-3 text-blue-500" />考试中心</h1>
                <p className="text-gray-500 mt-1">定期检验学习成果，与全站考生一较高下，精准定位知识盲区。</p>
            </div>

            <div className="space-y-6">
                <MonthlyTestCalendar onStartExam={handleStartExam} />
                <PastExamsCard exams={MOCK_PAST_EXAMS} onViewDetails={handleViewDetails} />
                <PerformanceTrendChart pastExams={MOCK_PAST_EXAMS} />
            </div>
        </div>
    );
};

export default CompetitionPage;