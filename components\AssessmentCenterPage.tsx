

import React, { useState, useMemo, useCallback, useEffect, FC } from 'react';
import { ActivePage, SubjectId, SubjectTopic, ArchetypeSession, ProblemArchetype, CoreConceptFlowchart, TopicAnalysisRequest, MasteryStatus, Prerequisite, ReferenceInfo, Question, User } from '../types';
import { TRAINING_TOPICS } from '../constants';
import * as api from '../services/apiService';
import { ArrowRightIcon, AcademicCapIcon, BoltIcon, BeakerIcon, CheckCircleIcon, StarIcon, XMarkIcon, PencilSquareIcon, QueueListIcon, ChartPieIcon, TrophyIcon, LockClosedIcon } from '@heroicons/react/24/solid';
import SubjectTopicSelector from './SubjectTopicSelector';
import ProgressBar from './ProgressBar';
import DataCenterPage from './DataCenterPage';
import MathRenderer from './MarkdownKatex';
import QuestionMarkCircleIcon from './icons/QuestionMarkCircleIcon';


// --- Sub-components moved inside for locality ---
type TabId = 'test' | 'history' | 'analysis';

const ReferenceModal: FC<{ reference: ReferenceInfo; onClose: () => void }> = ({ reference, onClose }) => (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[60] p-4" onClick={onClose}>
        <div className="bg-white rounded-lg shadow-xl w-full max-w-md animate-fade-in-fast" onClick={e => e.stopPropagation()}>
            <div className="p-5 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800">{reference.title}</h3>
            </div>
            <div className="p-6">
                <ul className="space-y-2 text-gray-700">
                    {reference.items.map((item, index) => (
                        <li key={index} className="text-base">{item}</li>
                    ))}
                </ul>
            </div>
            <div className="px-6 py-4 bg-gray-50 text-right rounded-b-lg">
                <button 
                    onClick={onClose} 
                    className="bg-green-600 text-white font-bold py-2 px-8 rounded-lg shadow-sm hover:bg-green-700 transition-colors"
                >
                    好的
                </button>
            </div>
        </div>
    </div>
);


const PrerequisitesModal: FC<{ topic: SubjectTopic; onConfirm: () => void; onClose: () => void; }> = ({ topic, onConfirm, onClose }) => {
    const [checkedItems, setCheckedItems] = useState<Set<string>>(new Set());
    const [activeReference, setActiveReference] = useState<ReferenceInfo | null>(null);

    const prerequisites = topic.prerequisites || [];
    const allChecked = checkedItems.size === prerequisites.length;

    const handleCheck = (id: string, isChecked: boolean) => {
        setCheckedItems(prev => {
            const newSet = new Set(prev);
            if (isChecked) newSet.add(id); else newSet.delete(id);
            return newSet;
        });
    };
    
    return (
        <div className="fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center z-50 p-4">
             {activeReference && <ReferenceModal reference={activeReference} onClose={() => setActiveReference(null)} />}
            <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col animate-fade-in-fast">
                <div className="p-5 border-b border-gray-200 flex justify-between items-center">
                    <h2 className="text-xl font-bold text-gray-800">你知道这些知识点吗?</h2>
                    <button onClick={onClose} className="text-gray-400 hover:text-gray-600"><XMarkIcon className="w-6 h-6"/></button>
                </div>
                <div className="p-6 flex-grow overflow-y-auto">
                    <p className="text-gray-600 mb-6">以下是本专题的主要知识点。在现阶段你了解是什么即可，之后我们会训练你运用。如果你知道这个知识点，请打勾。对不了解的知识点，请去建议的参考资料查看（或者其它资料均可）了解以后请打勾。</p>
                    <div className="space-y-3">
                        {prerequisites.map(prereq => (
                             <label key={prereq.id} className="flex items-start p-3 bg-gray-50 rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-100 transition">
                                <input 
                                    type="checkbox" 
                                    className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-0.5 shrink-0" 
                                    onChange={(e) => handleCheck(prereq.id, e.target.checked)} 
                                />
                                <div className="ml-3 text-gray-800 font-medium">
                                    <MathRenderer content={prereq.name} className="inline-flex items-center" />
                                    {prereq.reference && (
                                        <button 
                                            onClick={(e) => { e.preventDefault(); e.stopPropagation(); setActiveReference(prereq.reference!); }}
                                            className="text-sm text-blue-600 hover:text-blue-800 hover:underline ml-2"
                                        >
                                            (点击查看参考资料)
                                        </button>
                                    )}
                                </div>
                            </label>
                        ))}
                    </div>
                </div>
                <div className="p-5 bg-gray-50 border-t border-gray-200 text-right">
                    <button 
                        onClick={onConfirm}
                        disabled={!allChecked}
                        className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-8 rounded-lg shadow-md transition-all transform hover:scale-105 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:scale-100"
                    >
                        {allChecked ? "开始训练" : `还差 ${prerequisites.length - checkedItems.size} 项`}
                    </button>
                </div>
            </div>
        </div>
    );
};


const FlowchartRenderer: React.FC<{ flowchart: CoreConceptFlowchart }> = ({ flowchart }) => {
    const { nodes, edges } = flowchart;
    const startNode = nodes.find(n => n.type === 'start');
    const decisionNodes = nodes.filter(n => n.type === 'decision');
    
    const getNodeById = (id: string) => nodes.find(n => n.id === id);

    return (
        <div className="flex items-center justify-center p-4 rounded-lg">
            <div className="flex items-center">
                {/* Start Node */}
                <div className="text-center font-bold text-gray-800 bg-white p-4 rounded-lg shadow border border-gray-300">
                    <MathRenderer content={startNode?.label || ''} />
                </div>

                {/* Branching Lines */}
                <div className="flex items-center mx-4">
                     <div className="w-8 h-0.5 bg-gray-400"></div>
                     <div className="w-0.5 h-32 bg-gray-400"></div>
                </div>

                {/* Decision and Process Nodes */}
                <div className="flex flex-col space-y-2">
                    {decisionNodes.map(dNode => {
                         const edgeToProcess = edges.find(e => e.from === dNode.id);
                         const pNode = edgeToProcess ? getNodeById(edgeToProcess.to) : null;
                        return (
                            <div key={dNode.id} className="flex items-center">
                                <div className="w-8 h-0.5 bg-gray-400 -ml-8"></div>
                                <div className="text-center text-sm font-semibold text-blue-800 bg-blue-100 p-3 rounded-lg shadow-sm border border-blue-200 w-48">
                                    <MathRenderer content={dNode.label} />
                                </div>
                                <div className="w-4 h-0.5 bg-gray-400 mx-2"></div>
                                <ArrowRightIcon className="w-5 h-5 text-gray-400" />
                                <div className="text-left text-sm text-gray-700 bg-white p-3 rounded-lg shadow-sm border border-gray-200 w-64 ml-2">
                                     <MathRenderer content={pNode?.label || ''} />
                                </div>
                            </div>
                        )
                    })}
                </div>
            </div>
        </div>
    );
};


const TopicAnalysisReport: React.FC<{ selectedTopic: SubjectTopic }> = ({ selectedTopic }) => {
    
    const { level1Progress, timeToComplete, level1ArchetypeNames, allMastered } = useMemo(() => {
        const levels = selectedTopic.levels || [];
        const archetypes = selectedTopic.problemArchetypes || [];
        if (archetypes.length === 0) return { allMastered: false };
        
        const level1 = levels.find(l => l.level === 1);
        if (!level1) return { allMastered: false };

        const level1ArchetypeIds = new Set(level1.archetypeIds);
        const level1Archetypes = archetypes.filter(a => level1ArchetypeIds.has(a.id));
        if (level1Archetypes.length === 0) return { allMastered: false };

        const masteredCount = level1Archetypes.filter(a => a.masteryStatus === 'good_mastery').length;
        const progress = Math.round((masteredCount / level1Archetypes.length) * 100);
        
        // Estimate time: assume 15 mins per un-mastered archetype
        const timeInMinutes = (level1Archetypes.length - masteredCount) * 15;
        const timeInHours = (timeInMinutes / 60).toFixed(1);

        const allTopicArchetypesMastered = archetypes.every(a => a.masteryStatus === 'good_mastery');

        return {
            level1Progress: progress,
            timeToComplete: timeInHours,
            level1ArchetypeNames: level1Archetypes.map(a => a.name).join('、'),
            allMastered: allTopicArchetypesMastered
        };
    }, [selectedTopic]);


    if (!selectedTopic.problemArchetypes || selectedTopic.problemArchetypes.length === 0) {
        return <div className="p-8 text-center text-gray-500 bg-white rounded-lg">该专题暂无内容，无法生成分析报告。</div>;
    }
    
    if (allMastered) {
        return (
            <div className="bg-white p-6 md:p-8 rounded-2xl shadow-lg border border-gray-200 text-center animate-fade-in-fast">
                <TrophyIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
                <h2 className="text-2xl font-bold text-gray-800">太棒了，二狗！</h2>
                <p className="mt-2 text-gray-600">你已经完全掌握了【{selectedTopic.name}】专题的所有知识点！</p>
                <p className="mt-1 text-gray-600">可以选择其他专题继续挑战，或者去“考试”中心检验你的综合实力。</p>
            </div>
        );
    }

    return (
        <div className="bg-white p-6 md:p-8 rounded-2xl shadow-lg border border-gray-200 animate-fade-in-fast">
             <h2 className="text-2xl font-bold text-gray-800 mb-2">我们来聊聊【{selectedTopic.name}】这个硬骨头</h2>
             <p className="text-gray-500 mb-6">见字如面，二狗。这是我为你准备的专属战略分析，希望能帮你拨开迷雾。</p>
             
             <div className="prose prose-lg max-w-none text-gray-800 leading-relaxed">
                 <h4>这个专题在考研里有多重要？</h4>
                 <p>
                    咱们要攻克的 **{selectedTopic.name}**，是整个高等数学的基石，也是每年考研的**必考重点**。它不仅会以选择题、填空题的形式直接出现，更会作为“工具”，贯穿在后面导数、积分、中值定理等所有大题的解题步骤中。可以说，这里的掌握程度，直接决定了你高数能走多远。
                 </p>

                 <h4>你可能会遇到哪些“大Boss”？</h4>
                 <p>在这个专题里，主要有三类你需要征服的典型问题：</p>
                 <ol>
                    <li><strong>计算极限的“七种武器”：</strong> 这是核心中的核心。无论是未定式还是非未定式，你都需要熟练运用等价无穷小、洛必达、泰勒公式等多种方法组合求解。</li>
                    <li><strong>连续性与间断点的“侦探游戏”：</strong> 这类题会让你判断函数在某一点的连续性，并找出间断点的类型。关键在于对左极限、右极限和函数值的精确计算和比较。</li>
                    <li><strong>渐近线的“寻踪觅迹”：</strong> 考察你是否能通过求解特定形式的极限，找到函数的水平、垂直或斜渐近线。</li>
                 </ol>

                 <h4>我的通关秘籍</h4>
                 <p>
                    面对这些挑战，不要慌，咱们按部就班来。我的建议是：**先建立框架，再填充细节**。
                 </p>
                 <p>
                    首先，你要把求极限的各种方法（也就是我说的“七种武器”）整理成一张清单，形成一个决策流程：看到一个极限，先判断类型，然后按顺序尝试最简单的方法。比如，能用等价无穷小的，就绝不动用洛必达。这样能大大提高解题效率和准确率。你可以参考“训练”页的“核心解题框架”图，那就是你的作战地图。
                 </p>
                
                 <div className="bg-blue-50 border-l-4 border-blue-400 p-5 my-6 rounded-r-lg shadow-sm">
                     <h3 className="font-bold text-blue-900 !mt-0 !mb-2">🎯 你的当前阶段与目标</h3>
                     <p>
                        根据你的训练数据，目前你对**等级一（基础知识）**的掌握度大约是 <strong>{level1Progress}%</strong>。
                     </p>
                     <p>
                        要达到100%满级，意味着你需要对诸如“{level1ArchetypeNames}”等所有基础题型都了如指掌，形成条件反射。根据你的学习速度，我估计我们大约还需要 **{timeToComplete} 小时** 的专注练习时间。
                     </p>
                 </div>
                 
                 <p>
                    这份报告旨在为你指明方向。现在，你心里应该有数了。接下来，就让我们回到“**训练**”标签页，从“专项训练”开始，一步一个脚印，把这个硬骨头彻底啃下来。我在那里等你。
                 </p>
             </div>
         </div>
    );
};

interface AssessmentCenterPageProps {
    currentSubject: SubjectId;
    user: User;
    onNavigate: (page: ActivePage, context?: any) => void;
    activeBreakthroughSession: { topicId: string; archetypeId: string; } | null;
}

const AssessmentCenterPage: React.FC<AssessmentCenterPageProps> = ({ currentSubject, user, onNavigate, activeBreakthroughSession }) => {
  const trainingTopics = TRAINING_TOPICS[currentSubject] || [];
  const [selectedTopic, setSelectedTopic] = useState<SubjectTopic | null>(trainingTopics.length > 0 ? trainingTopics[0] : null);
  const [activeTab, setActiveTab] = useState<TabId>('test');
  
  const [isPrereqModalOpen, setIsPrereqModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const hasActiveBreakthrough = !!activeBreakthroughSession;

  const handleStartTrainingClick = useCallback(() => {
      if (!selectedTopic || hasActiveBreakthrough) return;
      setIsPrereqModalOpen(true);
  }, [selectedTopic, hasActiveBreakthrough]);

  const proceedToTraining = useCallback(async () => {
    setIsLoading(true);
    setIsPrereqModalOpen(false);
    
    if (!selectedTopic) {
        setIsLoading(false);
        return;
    }

    const session: ArchetypeSession = {
        topic: selectedTopic,
        level: 1, 
        questionCount: 0, 
        totalTimeSeconds: 0, 
    };
    
    onNavigate(ActivePage.TrainingFocus, { session });

    setIsLoading(false);
  }, [onNavigate, selectedTopic]);

  const handleContinueBreakthrough = useCallback(() => {
    if (!activeBreakthroughSession || !selectedTopic) return;
     const session: ArchetypeSession = {
        topic: selectedTopic,
        level: 1, 
        questionCount: 0, 
        totalTimeSeconds: 0, 
    };
    onNavigate(ActivePage.TrainingFocus, { session, activeBreakthroughSession });
  }, [activeBreakthroughSession, selectedTopic, onNavigate]);

  useEffect(() => {
    if (hasActiveBreakthrough) {
        const breakthroughTopic = trainingTopics.find(t => t.id === activeBreakthroughSession.topicId);
        if (breakthroughTopic) {
            setSelectedTopic(breakthroughTopic);
        }
    } else {
        // When subject changes or breakthrough is cleared, reset to the first topic
        setSelectedTopic(trainingTopics.length > 0 ? trainingTopics[0] : null);
    }
     setActiveTab('test'); // Reset to default tab
  }, [currentSubject, activeBreakthroughSession, hasActiveBreakthrough]);


  const { goodMasteryPercentage, observationPercentage, masterySegments } = useMemo(() => {
    if (!selectedTopic) return { goodMasteryPercentage: 0, observationPercentage: 0, masterySegments: [] };
    const archetypes = selectedTopic.problemArchetypes || [];
    if (archetypes.length === 0) return { goodMasteryPercentage: 0, observationPercentage: 0, masterySegments: [] };
    
    const totalArchetypes = archetypes.length;
    const goodCount = archetypes.filter(a => a.masteryStatus === 'good_mastery').length;
    const observationCount = archetypes.filter(a => a.masteryStatus === 'needs_observation').length;
    
    const goodPerc = (goodCount / totalArchetypes) * 100;
    const obsPerc = (observationCount / totalArchetypes) * 100;

    const segments = [
        { percentage: goodPerc, color: 'bg-primary-blue' },
        { percentage: obsPerc, color: 'bg-orange-400' }
    ];

    return { goodMasteryPercentage: goodPerc, observationPercentage: obsPerc, masterySegments: segments };
  }, [selectedTopic]);

  const renderTabContent = () => {
    if (!selectedTopic) {
         return <div className="text-center py-20 text-gray-500 bg-white rounded-lg">请先在上方选择一个专项进行学习。</div>;
    }
    
    switch(activeTab) {
        case 'history':
            return <DataCenterPage currentSubject={currentSubject} selectedTopic={selectedTopic} />;
        case 'analysis':
            return <TopicAnalysisReport selectedTopic={selectedTopic} />;
        case 'test':
        default:
            return (
                <div className="space-y-8 p-6 md:p-8">
                    <div className="bg-gray-50 p-6 rounded-2xl border border-gray-200">
                        <div className="flex items-center mb-6">
                            <AcademicCapIcon className="w-8 h-8 text-blue-600" />
                            <h1 className="ml-3 text-2xl font-bold text-gray-800">找到你的弱点, 才能最快突破</h1>
                        </div>
                        <p className="text-sm text-gray-500 mb-6">评估你对本专题的掌握程度，然后开始针对性训练。</p>
                        
                        <div className="mb-8">
                            <div className="mb-2 flex justify-between items-baseline">
                               <span className="font-semibold text-gray-800">当前【{selectedTopic.name}】专题掌握度</span>
                               <div className="text-sm font-medium">
                                    <span className="text-blue-600">已掌握 {goodMasteryPercentage.toFixed(0)}%</span>
                                    <span className="text-gray-400 mx-2">|</span>
                                    <span className="text-orange-500">待观察 {observationPercentage.toFixed(0)}%</span>
                               </div>
                            </div>
                            <div className="flex items-center">
                                <ProgressBar segments={masterySegments} />
                                 <div className="relative ml-3 group">
                                    <QuestionMarkCircleIcon className="w-6 h-6 text-gray-400 cursor-pointer" />
                                    <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-48 bg-gray-800 text-white text-xs rounded py-2 px-3 text-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                                        蓝色部分代表已熟练掌握的题型，橙色部分代表系统认为您可能需要复习的题型。
                                        <svg className="absolute text-gray-800 h-2 w-full left-0 top-full" x="0px" y="0px" viewBox="0 0 255 255"><polygon className="fill-current" points="0,0 127.5,127.5 255,0"/></svg>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {hasActiveBreakthrough && (
                            <div className="text-center p-4 mb-4 bg-yellow-100 border-l-4 border-yellow-400 rounded-r-lg">
                                <p className="font-semibold text-yellow-800">你有一个未完成的突破训练，请先完成它！</p>
                            </div>
                        )}

                         <div className="text-center pt-6 border-t border-gray-200">
                           <button 
                                onClick={hasActiveBreakthrough ? handleContinueBreakthrough : handleStartTrainingClick}
                                disabled={isLoading}
                                className="inline-flex items-center bg-blue-600 text-white font-bold rounded-full shadow-lg hover:bg-blue-700 transition transform hover:scale-105 focus:outline-none disabled:bg-gray-400 disabled:scale-100"
                            >
                                <span className="pl-8 pr-6 py-3 text-lg">{isLoading ? '准备中...' : (hasActiveBreakthrough ? '继续上次的突破训练' : '开始专项训练')}</span>
                                <span className="bg-blue-800 h-full p-3 rounded-full mr-1">
                                    {hasActiveBreakthrough ? <LockClosedIcon className="w-6 h-6 text-white"/> : <ArrowRightIcon className="w-6 h-6 text-white"/>}
                                </span>
                            </button>
                        </div>
                    </div>
                    
                    <div className="bg-gray-50 p-6 rounded-2xl border border-gray-200">
                        <h2 className="text-xl font-bold text-gray-800 mb-4">
                            {selectedTopic.name} - 核心解题框架
                        </h2>
                        {selectedTopic.flowchart ? (
                            <FlowchartRenderer flowchart={selectedTopic.flowchart} />
                        ) : (
                            <div className="text-center text-gray-500 p-8 border rounded-lg bg-gray-50">该专题暂无核心框架图。</div>
                        )}
                    </div>
                </div>
            );
    }
  }

  const TabButton: FC<{ tabId: TabId, activeTab: TabId, onClick: (tabId: TabId) => void, icon: React.FC<any>, label: string }> = ({ tabId, activeTab, onClick, icon: Icon, label }) => (
      <button onClick={() => onClick(tabId)} className={`flex items-center whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm transition-colors duration-200 ${activeTab === tabId ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}>
          <Icon className="w-5 h-5 mr-2" /> {label}
      </button>
  );

  return (
    <div className="min-h-full flex flex-col">
        {isPrereqModalOpen && selectedTopic && (
            <PrerequisitesModal 
                topic={selectedTopic} 
                onConfirm={proceedToTraining} 
                onClose={() => setIsPrereqModalOpen(false)} 
            />
        )}
        <div className="border-b border-gray-200">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center">
                    <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                        <TabButton tabId="test" activeTab={activeTab} onClick={setActiveTab} icon={PencilSquareIcon} label="训练" />
                        <TabButton tabId="history" activeTab={activeTab} onClick={setActiveTab} icon={QueueListIcon} label="历史" />
                        <TabButton tabId="analysis" activeTab={activeTab} onClick={setActiveTab} icon={ChartPieIcon} label="本专题分析" />
                    </nav>
                    <div className={`w-64 py-2 ${hasActiveBreakthrough ? 'opacity-50 pointer-events-none' : ''}`}>
                         <SubjectTopicSelector
                            label=""
                            topics={trainingTopics}
                            selectedTopic={selectedTopic}
                            onSelectTopic={(topic) => {setSelectedTopic(topic);}}
                            disabled={hasActiveBreakthrough}
                        />
                    </div>
                </div>
            </div>
        </div>
        
        <div className="flex-grow">
             <div className="max-w-7xl mx-auto sm:px-6 lg:px-8 h-full">
                {renderTabContent()}
            </div>
        </div>
    </div>
  );
};

export default AssessmentCenterPage;