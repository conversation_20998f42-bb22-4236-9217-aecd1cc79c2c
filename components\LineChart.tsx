
import React from 'react';
import { <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from 'recharts';
import { ProgressChartDataPoint } from '../types';

interface LineChartProps {
  data: ProgressChartDataPoint[];
  lineKey: keyof ProgressChartDataPoint; // key for the data to plot on Y axis
  xAxisKey: keyof ProgressChartDataPoint; // key for X axis labels
  lineColor?: string;
}

const LineChartComponent: React.FC<LineChartProps> = ({ data, lineKey, xAxisKey, lineColor = "#8884d8" }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <ReLineChart
        data={data}
        margin={{
          top: 5, right: 30, left: 0, bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
        <XAxis dataKey={xAxisKey} tick={{ fontSize: 12, fill: '#666' }} />
        <YAxis 
            tickFormatter={(value) => `${value}%`} 
            domain={[0, 100]} 
            tick={{ fontSize: 12, fill: '#666' }} 
        />
        <Tooltip
          formatter={(value: number) => [`${value}%`, "正确率"]}
          labelStyle={{ color: '#333' }}
          itemStyle={{ color: lineColor }}
        />
        <Legend wrapperStyle={{ fontSize: '14px' }} />
        <Line type="monotone" dataKey={lineKey} stroke={lineColor} strokeWidth={2} activeDot={{ r: 6 }} dot={{r: 4}} name="预测正确率" />
      </ReLineChart>
    </ResponsiveContainer>
  );
};

export default LineChartComponent;
