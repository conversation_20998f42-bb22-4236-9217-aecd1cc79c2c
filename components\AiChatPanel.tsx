

import React, { useState, useEffect, useRef, useCallback } from 'react';
import <PERSON><PERSON>enderer from './MarkdownKatex';
import { getAIExplanationForStep } from '../services/apiService';
import SparklesIcon from './icons/SparklesIcon';
import XMarkIcon from './icons/XMarkIcon';
import PaperAirplaneIcon from './icons/PaperAirplaneIcon';
import ArrowPathIcon from './icons/ArrowPathIcon';

interface ChatMessage {
    role: 'user' | 'model';
    content: string;
}

interface AiChatPanelProps {
    isOpen: boolean;
    onClose: () => void;
    initialPromptInfo: { prompt: string, key: number };
}

const MIN_PANEL_WIDTH = 320; // 20rem
const MAX_PANEL_WIDTH = 800; // 50rem

const AiChatPanel: React.FC<AiChatPanelProps> = ({ isOpen, onClose, initialPromptInfo }) => {
    const [panelWidth, setPanelWidth] = useState(384); // Default: w-96
    const [isResizing, setIsResizing] = useState(false);
    const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [input, setInput] = useState('');

    const panelRef = useRef<HTMLDivElement>(null);
    const isResizingRef = useRef(false);
    const endOfMessagesRef = useRef<HTMLDivElement>(null);
    const isSendingRef = useRef(false);
    const lastProcessedKeyRef = useRef<number>(0);

    const handleMouseDown = (e: React.MouseEvent) => {
        e.preventDefault();
        isResizingRef.current = true;
        setIsResizing(true);
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';
    };

    const handleMouseMove = useCallback((e: MouseEvent) => {
        if (!isResizingRef.current || !panelRef.current) return;
        
        const containerRect = panelRef.current.parentElement?.getBoundingClientRect();
        if (!containerRect) return;

        let newWidth = containerRect.right - e.clientX;
        
        if (newWidth < MIN_PANEL_WIDTH) newWidth = MIN_PANEL_WIDTH;
        if (newWidth > MAX_PANEL_WIDTH) newWidth = MAX_PANEL_WIDTH;
        
        setPanelWidth(newWidth);
    }, []);

    const handleMouseUp = useCallback(() => {
        if (isResizingRef.current) {
            isResizingRef.current = false;
            setIsResizing(false);
            document.body.style.cursor = '';
            document.body.style.userSelect = '';
        }
    }, []);

    useEffect(() => {
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        return () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, [handleMouseMove, handleMouseUp]);


    const handleSendMessage = useCallback(async (message: string) => {
        if (!message.trim() || isSendingRef.current) return;
        
        isSendingRef.current = true;
        setChatHistory(prev => [...prev, { role: 'user' as const, content: message }]);
        setIsLoading(true);

        try {
            const problemContext = "用户正在进行考研数学练习。";
            const response = await getAIExplanationForStep(problemContext, message);
            setChatHistory(prev => [...prev, { role: 'model', content: response }]);
        } catch (error) {
            console.error(error);
            setChatHistory(prev => [...prev, { role: 'model', content: '抱歉，AI服务暂时遇到问题，请稍后再试。' }]);
        } finally {
            setIsLoading(false);
            isSendingRef.current = false;
        }
    }, []);
    
    const handleResetChat = () => {
        setChatHistory([]);
        setIsLoading(false);
        isSendingRef.current = false;
        setInput('');
    }

    useEffect(() => {
        if (initialPromptInfo.prompt && initialPromptInfo.key > lastProcessedKeyRef.current) {
            handleResetChat();
            handleSendMessage(initialPromptInfo.prompt);
            lastProcessedKeyRef.current = initialPromptInfo.key;
        }
    }, [initialPromptInfo, handleSendMessage]);
    
    useEffect(() => {
        endOfMessagesRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [chatHistory, isLoading]);
    
    const handleSendClick = () => {
        if (input.trim()) {
            handleSendMessage(input.trim());
            setInput('');
        }
    };

    const panelClasses = `relative flex-shrink-0 ${isResizing ? '' : 'transition-all duration-300 ease-in-out'}`;

    return (
        <div
            ref={panelRef}
            className={panelClasses}
            style={{ width: isOpen ? `${panelWidth}px` : '0px' }}
            aria-hidden={!isOpen}
        >
            {/* Resizer handle */}
            <div
                className="absolute top-0 -left-1 w-2 h-full cursor-col-resize z-10 group"
                onMouseDown={handleMouseDown}
                aria-label="Resize panel"
                role="separator"
                aria-orientation="vertical"
            >
                <div className="w-0.5 h-full bg-transparent group-hover:bg-blue-400 transition-colors mx-auto"></div>
            </div>
            
            <div 
                className="w-full h-full bg-white rounded-xl shadow-sm flex flex-col overflow-hidden transition-opacity duration-200 ease-in"
                style={{ opacity: isOpen ? 1 : 0 }}
            >
                {/* Header */}
                <div className="h-16 p-4 border-b border-gray-200 flex justify-between items-center shrink-0">
                    <h2 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                        <SparklesIcon className="w-5 h-5 text-blue-500" />
                        AI 助教
                    </h2>
                    <div className="flex items-center gap-2">
                        <button onClick={handleResetChat} className="p-2 rounded-lg text-gray-500 hover:bg-gray-200" title="清空对话">
                            <ArrowPathIcon className="w-5 h-5"/>
                        </button>
                        <button onClick={onClose} className="p-2 rounded-lg text-gray-500 hover:bg-gray-200" title="关闭面板">
                            <XMarkIcon className="w-6 h-6"/>
                        </button>
                    </div>
                </div>

                {/* Messages */}
                <div className="flex-grow p-4 overflow-y-auto space-y-4">
                    {chatHistory.length === 0 && !isLoading && (
                        <div className="text-center text-gray-500 text-sm pt-2">
                            <p>你好，我是你的AI助教小圆。<br/>有什么数学问题都可以问我哦！</p>
                        </div>
                    )}
                    {chatHistory.map((msg, index) => (
                        <div key={index} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                            <div className={`p-3 rounded-lg max-w-xs prose prose-sm ${msg.role === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'}`}>
                            <MathRenderer content={msg.content} />
                            </div>
                        </div>
                    ))}
                    {isLoading && (
                        <div className="flex justify-start">
                            <div className="p-3 rounded-lg bg-gray-200 text-gray-500 flex items-center gap-2">
                                <ArrowPathIcon className="w-4 h-4 animate-spin"/> 正在思考...
                            </div>
                        </div>
                    )}
                    <div ref={endOfMessagesRef} />
                </div>

                {/* Footer / Input */}
                <div className="p-4 border-t border-gray-200 shrink-0 bg-white/50">
                    <div className="flex gap-2">
                        <textarea 
                            value={input}
                            onChange={e => setInput(e.target.value)}
                            onKeyDown={e => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); handleSendClick(); }}}
                            placeholder="输入你的问题..."
                            className="flex-grow p-2 border border-gray-300 rounded-md resize-none text-sm focus:ring-2 focus:ring-blue-400 focus:border-transparent bg-white"
                            rows={2}
                        />
                        <button onClick={handleSendClick} className="bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 self-end" disabled={!input.trim() || isLoading}>
                            <PaperAirplaneIcon className="w-5 h-5"/>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AiChatPanel;