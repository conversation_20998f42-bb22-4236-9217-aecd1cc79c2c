



import React, { useState, useEffect, useRef, useCallback, useMemo, FC } from 'react';
import { Exam, ActivePage, Question, QuestionType } from '../types';
import MathRenderer from './MarkdownKatex';
import { 
    ClockIcon, ChevronLeftIcon, ChevronRightIcon, XMarkIcon, ExclamationTriangleIcon, HandRaisedIcon, InformationCircleIcon, CheckIcon, MinusIcon, XCircleIcon, CheckCircleIcon, BookOpenIcon, QueueListIcon
} from '@heroicons/react/24/solid';

const ConfirmationModal: FC<{ title: string, message: string, confirmText: string, confirmColor?: string, cancelText?: string, onConfirm: () => void, onCancel: () => void }> = ({ title, message, confirmText, confirmColor = 'bg-red-500 hover:bg-red-600', cancelText = "取消", onConfirm, onCancel }) => (
     <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-[100] p-4">
        <div className="bg-white rounded-lg shadow-2xl p-6 w-full max-w-md text-center animate-fade-in-fast">
             <ExclamationTriangleIcon className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
             <h3 className="text-xl font-bold text-gray-900">{title}</h3>
             <p className="text-gray-600 my-4">{message}</p>
             <div className="flex justify-center gap-4">
                <button onClick={onCancel} className="px-6 py-2 rounded-lg bg-gray-200 text-gray-800 hover:bg-gray-300 font-semibold">{cancelText}</button>
                <button onClick={onConfirm} className={`px-6 py-2 rounded-lg text-white font-semibold ${confirmColor}`}>{confirmText}</button>
             </div>
        </div>
     </div>
);

const LegendPopup: FC<{ onClose: () => void }> = ({ onClose }) => (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex justify-center items-center z-[100]" onClick={onClose}>
        <div className="bg-white rounded-xl shadow-2xl p-6 w-full max-w-sm animate-fade-in-fast" onClick={e => e.stopPropagation()}>
            <div className="grid grid-cols-2 gap-x-6 gap-y-6 text-center">
                <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center"></div>
                    <p className="mt-2 text-sm text-gray-700">未作答</p>
                </div>
                <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-lg bg-blue-50 border-2 border-blue-200 text-blue-500 flex items-center justify-center">
                        <MinusIcon className="w-6 h-6"/>
                    </div>
                    <p className="mt-2 text-sm text-gray-700">标记待评</p>
                </div>
                <div className="flex flex-col items-center">
                    <div className="w-12 h-12 rounded-lg bg-green-50 border-2 border-green-200 text-green-500 flex items-center justify-center">
                        <CheckIcon className="w-7 h-7"/>
                    </div>
                    <p className="mt-2 text-sm text-gray-700">已作答</p>
                </div>
                 <div className="flex flex-col items-center">
                     <div className="w-12 h-12 rounded-lg bg-green-100 border-2 border-green-300 text-green-600 flex items-center justify-center">
                        <CheckCircleIcon className="w-7 h-7" />
                    </div>
                    <p className="mt-2 text-sm text-gray-700">答案正确</p>
                </div>
                 <div className="flex flex-col items-center">
                     <div className="w-12 h-12 rounded-lg bg-red-100 border-2 border-red-300 text-red-600 flex items-center justify-center">
                        <XCircleIcon className="w-7 h-7" />
                    </div>
                    <p className="mt-2 text-sm text-gray-700">答案错误</p>
                </div>
            </div>
        </div>
    </div>
);

const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60).toString().padStart(2, '0');
    const secs = (seconds % 60).toString().padStart(2, '0');
    return `${mins}:${secs}`;
};

const QuestionRenderer: FC<{ question: Question; userAnswer: string | undefined; isReviewMode: boolean; onAnswerChange: (questionId: string, answer: string) => void; }> = ({ question, userAnswer, isReviewMode, onAnswerChange }) => {
    const questionBody = question.questionText;
    const options = question.options || [];
    
    const questionBodyParts = useMemo(() => {
        const regex = /(\$.*?\$)/g;
        return questionBody.split(regex).filter(p => p);
    }, [questionBody]);


    const isCorrect = userAnswer === question.standardAnswer;

    return (
        <div>
             <div className="text-xl max-w-none mb-8 text-gray-800 leading-relaxed flex items-center flex-wrap gap-x-2">
                {questionBodyParts.map((part, index) => {
                    if (part.startsWith('$') && part.endsWith('$')) {
                        return <MathRenderer key={index} content={part} />;
                    } else {
                        return <span key={index}>{part}</span>;
                    }
                })}
            </div>

            {question.type === '单选题' && (
                <div className="space-y-3">
                    {options.map((optionText, idx) => {
                        const letter = String.fromCharCode(65 + idx);
                        const isSelected = userAnswer === letter;
                        const isCorrectAnswer = question.standardAnswer === letter;

                        let bgClass = "bg-white hover:bg-gray-50";
                        let borderClass = "border-gray-200";

                        if (isReviewMode) {
                            if (isSelected && !isCorrect) { bgClass = "bg-red-50"; borderClass="border-red-300"; }
                            else if (isCorrectAnswer) { bgClass = "bg-green-50"; borderClass="border-green-300"; }
                            else bgClass = "bg-gray-50";
                        } else if (isSelected) {
                             bgClass = "bg-blue-50";
                             borderClass = "border-blue-400";
                        }

                        const optionContentParts = optionText.split(/(\$.*?\$)/g).filter(p => p);

                        return (
                             <label key={letter} className={`flex items-start p-3 rounded-lg border transition-colors cursor-pointer ${bgClass} ${borderClass}`}>
                                <input
                                    type="radio"
                                    name={question.id}
                                    value={letter}
                                    checked={isSelected}
                                    onChange={() => onAnswerChange(question.id, letter)}
                                    disabled={isReviewMode}
                                    className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 disabled:opacity-50 mt-1"
                                />
                                <div className="ml-3 text-lg flex-grow flex items-center flex-wrap">
                                    <span className="mr-2 font-semibold">{`${letter}.`}</span>
                                    {optionContentParts.map((part, index) => {
                                        if (part.startsWith('$') && part.endsWith('$')) {
                                            return <MathRenderer key={index} content={part} />;
                                        }
                                        return <span key={index}>{part}</span>;
                                    })}
                                </div>
                                {isReviewMode && isCorrectAnswer && <CheckCircleIcon className="w-6 h-6 text-green-600 ml-4 shrink-0" />}
                                {isReviewMode && isSelected && !isCorrect && <XCircleIcon className="w-6 h-6 text-red-600 ml-4 shrink-0" />}
                            </label>
                        );
                    })}
                </div>
            )}
            {(question.type !== '单选题' && question.type !== '多选题') && (
                 <input
                    type="text"
                    value={userAnswer || ''}
                    onChange={(e) => onAnswerChange(question.id, e.target.value)}
                    placeholder="在此处输入你的答案..."
                    className="w-full max-w-md p-3 border-2 border-gray-300 rounded-lg text-lg focus:border-blue-500 focus:ring-blue-500 transition-colors disabled:bg-gray-100"
                    disabled={isReviewMode}
                />
            )}
            {isReviewMode && (
                <div className="mt-8 bg-gray-50 p-6 rounded-lg border border-gray-200">
                    <table className="w-full text-left">
                        <tbody>
                            <tr className="border-b border-gray-200">
                                <td className="py-2 pr-4 text-gray-500 font-semibold">评测结果</td>
                                <td className="py-2 text-lg font-bold">{isCorrect ? <span className="text-green-600">答案正确</span> : <span className="text-red-600">答案错误</span>}</td>
                            </tr>
                            <tr className="border-b border-gray-200">
                                <td className="py-2 pr-4 text-gray-500 font-semibold">得分</td>
                                <td className="py-2 text-lg font-bold">{isCorrect ? '2分' : '0分'}</td>
                            </tr>
                            <tr>
                                <td className="py-2 pr-4 text-gray-500 font-semibold align-top">参考答案</td>
                                <td className="py-2 prose prose-lg">
                                    <MathRenderer content={question.detailedSolution} />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            )}
        </div>
    );
};

const ExamNavButton: FC<{ icon: React.FC<any>; label: string; isActive: boolean; onClick: () => void; }> = ({ icon: Icon, label, isActive, onClick }) => (
    <button
        onClick={onClick}
        className={`flex flex-col items-center justify-center w-full aspect-square rounded-lg transition-colors ${isActive ? 'bg-blue-100 text-blue-600' : 'bg-transparent text-gray-500 hover:bg-gray-200'}`}
        aria-label={label}
        title={label}
    >
        <Icon className="w-7 h-7" />
        <span className="text-xs font-semibold mt-1">{label}</span>
    </button>
);


interface ExamPageProps {
  session: Exam;
  onNavigate: (page: ActivePage) => void;
}

const ExamPage: React.FC<ExamPageProps> = ({ session, onNavigate }) => {
    const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
    const [userAnswers, setUserAnswers] = useState<Map<string, string>>(new Map());
    const [markedQuestions, setMarkedQuestions] = useState<Set<string>>(new Set());
    const [timeLeft, setTimeLeft] = useState(session.totalTimeSeconds);
    const [showConfirmation, setShowConfirmation] = useState<'exit' | 'submit' | null>(null);
    const [showLegend, setShowLegend] = useState(false);
    const [isReviewMode, setIsReviewMode] = useState(false);
    const [activeNav, setActiveNav] = useState('list'); // 'overview', 'list', 'submit'
    const [activeCategoryKey, setActiveCategoryKey] = useState<string | null>(null);

    const questionRefs = useRef<Map<string, HTMLElement | null>>(new Map());

    useEffect(() => {
        if (timeLeft <= 0) {
            handleConfirmSubmit();
            return;
        }
        if (isReviewMode) return;
        
        const timer = setInterval(() => {
            setTimeLeft(prev => prev - 1);
        }, 1000);
        return () => clearInterval(timer);
    }, [timeLeft, isReviewMode]);
    
    const { categorizedQuestions, categoryOrder, allQuestionData } = useMemo(() => {
        const categories = new Map<string, { question: Question, originalIndex: number }[]>();
        const categoryOrder: string[] = [];
        const allQuestionData: { question: Question; originalIndex: number }[] = [];

        session.questions.forEach((q, index) => {
            const categoryName = q.type || '未分类';
            if (!categories.has(categoryName)) {
                categories.set(categoryName, []);
                categoryOrder.push(categoryName);
            }
            categories.get(categoryName)!.push({ question: q, originalIndex: index });
            allQuestionData.push({ question: q, originalIndex: index });
        });
        
        return { categorizedQuestions: categories, categoryOrder, allQuestionData };
    }, [session.questions]);

    useEffect(() => {
        if (categoryOrder.length > 0 && !activeCategoryKey) {
            setActiveCategoryKey(categoryOrder[0]);
        }
    }, [categoryOrder, activeCategoryKey]);

    const handleAnswerChange = useCallback((questionId: string, answer: string) => {
        setUserAnswers(prev => new Map(prev).set(questionId, answer));
    }, []);
    
    const handleJumpTo = (index: number) => {
        setCurrentQuestionIndex(index);
        const questionId = session.questions[index]?.id;
        if (questionId) {
            const element = questionRefs.current.get(questionId);
            element?.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    };
    
    const handleToggleMark = (questionId: string) => {
        setMarkedQuestions(prev => {
            const newSet = new Set(prev);
            if (newSet.has(questionId)) {
                newSet.delete(questionId);
            } else {
                newSet.add(questionId);
            }
            return newSet;
        });
    };

    const handleConfirmSubmit = () => {
        console.log("Submitting answers:", Object.fromEntries(userAnswers));
        setShowConfirmation(null);
        setIsReviewMode(true);
        window.scrollTo(0,0);
    };

    const handleConfirmExit = () => onNavigate(ActivePage.Competition);

    const renderAnswerButton = (q: Question, index: number) => {
        const isCurrent = index === currentQuestionIndex;
        const userAnswer = userAnswers.get(q.id);
        const isAnswered = userAnswer?.trim() !== '' && userAnswer !== undefined;
        const isMarked = markedQuestions.has(q.id);
        
        let buttonClass = "w-10 h-10 rounded-lg border-2 transition-all flex items-center justify-center relative font-mono";
        let content: React.ReactNode;
        
        if (isReviewMode) {
            const isCorrect = userAnswer === q.standardAnswer;
            if (isCorrect) {
                buttonClass += " bg-green-100 border-green-300 text-green-600";
                content = <CheckCircleIcon className="w-7 h-7" />;
            } else {
                buttonClass += " bg-red-100 border-red-300 text-red-600";
                content = <XCircleIcon className="w-7 h-7" />;
            }
        } else if (isMarked) {
            buttonClass += " bg-blue-50 border-blue-200 text-blue-500";
            content = <MinusIcon className="w-6 h-6" />;
        } else if (isAnswered) {
            buttonClass += " bg-green-50 border-green-200 text-green-500";
            content = <CheckIcon className="w-7 h-7" />;
        } else {
            buttonClass += ` border-dashed border-gray-300 ${isCurrent ? 'text-blue-600' : 'text-gray-500'}`;
            content = <span className={`text-lg ${isCurrent ? 'font-bold':''}`}>{index + 1}</span>;
        }
        
        if (isCurrent && !isReviewMode) {
            buttonClass += " ring-2 ring-offset-1 ring-blue-500";
        }
        
        return (
            <button
                key={q.id}
                onClick={() => handleJumpTo(index)}
                className={buttonClass}
                aria-label={`跳转至第 ${index + 1} 题`}
            >
                {content}
            </button>
        );
    }
    
    return (
        <div className="flex h-screen w-screen bg-gray-100 font-sans">
            {showLegend && <LegendPopup onClose={() => setShowLegend(false)} />}
            {showConfirmation === 'exit' && (
                <ConfirmationModal 
                    title="确认退出"
                    message={isReviewMode ? "确定要退出回顾吗？" : "现在退出考试，你的作答将不会被保存。确定要退出吗？"}
                    confirmText="确认退出"
                    onConfirm={handleConfirmExit}
                    onCancel={() => setShowConfirmation(null)}
                />
            )}
            {showConfirmation === 'submit' && (
                <ConfirmationModal 
                    title="确认交卷"
                    message={`你还有 ${formatTime(timeLeft)} 的答题时间。确定要提交试卷吗？`}
                    confirmText="确认交卷"
                    confirmColor="bg-green-600 hover:bg-green-700"
                    onConfirm={handleConfirmSubmit}
                    onCancel={() => setShowConfirmation(null)}
                />
            )}

            {/* Left Sidebar */}
            <div className="w-[380px] flex-shrink-0 flex border-r border-gray-200 bg-white shadow-md">
                
                {/* Far-left Icon Nav */}
                <div className="w-24 bg-gray-50 border-r border-gray-200 p-3 flex flex-col items-center justify-between">
                    <div className="space-y-2 w-full">
                        <ExamNavButton icon={ClockIcon} label="概览" isActive={activeNav === 'overview'} onClick={() => setActiveNav('overview')} />
                        <ExamNavButton icon={BookOpenIcon} label="题目列表" isActive={activeNav === 'list'} onClick={() => setActiveNav('list')} />
                        <ExamNavButton icon={QueueListIcon} label="提交列表" isActive={activeNav === 'submit'} onClick={() => setActiveNav('submit')} />
                    </div>
                    <div>
                         <button 
                            onClick={() => setShowConfirmation('submit')}
                            className="w-full flex flex-col items-center justify-center aspect-square rounded-lg bg-green-100 text-green-700 hover:bg-green-200"
                            disabled={isReviewMode}
                            title="交卷"
                        >
                            <HandRaisedIcon className="w-7 h-7" />
                            <span className="text-xs font-semibold mt-1">交卷</span>
                        </button>
                    </div>
                </div>

                {/* Answer Card */}
                <div className="flex-1 flex flex-col">
                    <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 className="font-bold text-lg text-gray-800">题目总览</h2>
                        <span className="font-mono text-gray-500 text-sm">
                            {userAnswers.size} / {session.questions.length}
                        </span>
                    </div>

                    <div className="p-4 border-b border-gray-200">
                        {categoryOrder.map(catKey => {
                            const catQuestions = categorizedQuestions.get(catKey);
                            if (!catQuestions) return null;
                            const isCatActive = activeCategoryKey === catKey;
                             const answeredCount = catQuestions.filter(q => userAnswers.has(q.question.id)).length;
                            return (
                                <button key={catKey} onClick={() => setActiveCategoryKey(catKey)} className={`w-full p-2 rounded-md text-left flex justify-between items-center transition-colors mb-1 ${isCatActive ? 'bg-blue-50' : 'hover:bg-gray-100'}`}>
                                    <span className={`font-semibold ${isCatActive ? 'text-blue-700' : 'text-gray-800'}`}>{catKey}</span>
                                    <span className={`text-sm font-mono ${isCatActive ? 'text-blue-600' : 'text-gray-500'}`}>{answeredCount} / {catQuestions.length}</span>
                                </button>
                            );
                        })}
                    </div>
                    
                    <div className="flex-grow p-4 overflow-y-auto">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="font-bold text-gray-800">{activeCategoryKey}</h3>
                            <button onClick={() => setShowLegend(true)} className="flex items-center gap-1 text-sm text-gray-500 hover:text-blue-600">
                                <InformationCircleIcon className="w-5 h-5" />
                                <span>图例</span>
                            </button>
                        </div>
                        <div className="grid grid-cols-5 gap-2">
                             {(categorizedQuestions.get(activeCategoryKey || '') || []).map(({ question: q, originalIndex: index }) => renderAnswerButton(q, index))}
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Question Area */}
             <div className="flex-1 flex flex-col bg-white overflow-hidden">
                <header className="shrink-0 bg-white border-b border-gray-200 px-6 py-3 flex justify-between items-center">
                    <h1 className="text-xl font-bold text-gray-800">{isReviewMode ? `${session.name} - 考试回顾` : session.name}</h1>
                    <div className="flex items-center gap-4">
                        {!isReviewMode && (
                            <div className={`flex items-center gap-2 font-mono text-2xl font-bold ${timeLeft < 300 ? 'text-red-500' : 'text-gray-900'}`}>
                                <ClockIcon className="w-7 h-7" />
                                <span>{formatTime(timeLeft)}</span>
                            </div>
                        )}
                        <button onClick={() => setShowConfirmation('exit')} className="text-gray-500 hover:text-red-600 p-2 rounded-full">
                            <XMarkIcon className="w-6 h-6" />
                        </button>
                    </div>
                </header>
                <div className="flex-1 overflow-y-auto">
                    <div className="px-10 lg:px-20 py-10 space-y-12">
                         {(categorizedQuestions.get(activeCategoryKey || '') || []).map(({ question, originalIndex: index }) => (
                            <div key={question.id} id={question.id} ref={(el) => { if (el) questionRefs.current.set(question.id, el); }} className="border-b border-gray-200 pb-12 last:border-b-0">
                                <div className="flex justify-between items-center mb-6">
                                    <h2 className="text-xl font-bold text-gray-800 flex items-baseline gap-2">
                                        <span>{`${index + 1}.`}</span>
                                        <span className="font-normal text-gray-600">({question.type})</span>
                                    </h2>
                                    {!isReviewMode && (
                                        <button onClick={() => handleToggleMark(question.id)} className={`flex items-center gap-2 font-semibold py-1.5 px-4 rounded-full text-sm transition-colors ${markedQuestions.has(question.id) ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'}`}>
                                            <MinusIcon className="w-5 h-5"/>
                                            {markedQuestions.has(question.id) ? '取消待评' : '标记待评'}
                                        </button>
                                    )}
                                </div>
                                <QuestionRenderer
                                    question={question}
                                    userAnswer={userAnswers.get(question.id)}
                                    isReviewMode={isReviewMode}
                                    onAnswerChange={handleAnswerChange}
                                />
                            </div>
                         ))}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default ExamPage;