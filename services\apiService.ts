
import { GoogleGenAI, GenerateContentResponse, Type } from "@google/genai";
import { KnowledgePoint, Question, TopicAnalysisRequest, ProblemArchetype, User, QuestionType, ProblemArchetypeDetails, Exam } from '../types';
import { MOCK_WEEKLY_EXAM } from '../constants';

const API_KEY = process.env.API_KEY;
let ai: GoogleGenAI | null = null;

if (API_KEY) {
  ai = new GoogleGenAI({ apiKey: API_KEY });
} else {
  console.warn("API_KEY for Gemini is not set in process.env. AI features will be mocked. Ensure API_KEY is configured in your environment.");
}

const TEXT_MODEL = 'gemini-2.5-flash';

// --- Backend API Service ---

// Set the base URL based on the provided Spring Boot log.
const API_BASE_URL = 'http://localhost:8080/'; 

const getToken = () => localStorage.getItem('authToken');

// Generic fetch wrapper for backend APIs
async function fetchApi(url: string, options: RequestInit = {}) {
    const token = getToken();
    
    const finalHeaders = new Headers(options.headers);
    
    if (options.body && !finalHeaders.has('Content-Type')) {
      finalHeaders.set('Content-Type', 'application/json');
    }

    if (token) {
        // Per user's provided axios example, the backend expects the token directly
        // without the "Bearer " prefix.
        finalHeaders.set('Authorization', token);
    }

    const response = await fetch(url, { ...options, headers: finalHeaders });
    
    // The API seems to return JSON even for errors
    const responseData = await response.json();

    if (!response.ok) {
        throw new Error(responseData?.message || `HTTP error! status: ${response.status}`);
    }
    
    if (responseData.code !== 0) {
        throw new Error(responseData.message || 'API request failed');
    }

    return responseData.data;
}

// --- Auth Functions ---

export const login = (username: string, password: string): Promise<string> => {
    const url = `${API_BASE_URL}users/login`;
    return fetchApi(url, {
        method: 'POST',
        body: JSON.stringify({
            username,
            password,
        }),
    });
};

export const register = (username: string, password: string): Promise<null> => {
    // Based on the provided API tool screenshot for /users/register,
    // the backend expects the password field to be named 'hashedPassword'.
    // Although this naming is unconventional for a raw password, we are
    // aligning with the provided request format to ensure compatibility.
    return fetchApi(`${API_BASE_URL}users/register`, {
        method: 'POST',
        body: JSON.stringify({
            username,
            hashedPassword: password
        }),
    });
};

export const getUserInfo = (): Promise<User> => {
    return fetchApi(`${API_BASE_URL}users/userInfo`, { method: 'GET' });
};


// --- Data Fetching Functions ---

const getQuestionTypeFromData = (questionData: any): QuestionType => {
    if (Array.isArray(questionData.options) && questionData.options.length > 0) {
        return '单选题';
    }
    if (questionData.questionText.includes('计算')) {
        return '计算题';
    }
    if (questionData.questionText.includes('证明')) {
        return '简答题';
    }
    return '填空题';
}

export const getInitialAssessmentQuestions = async (knowledgePointId: string): Promise<Question[]> => {
    const url = new URL('api/v1/questions/questionQuery', API_BASE_URL);

    if (knowledgePointId === undefined || knowledgePointId === null) {
        const errorMessage = "获取初始题目失败：调用时未提供 knowledgePointId。";
        console.error(errorMessage);
        throw new Error(errorMessage);
    }
    url.searchParams.append('knowledgePointId', knowledgePointId);

    try {
        const data = await fetchApi(url.toString(), { method: 'GET' });
        
        if (!Array.isArray(data)) {
            console.warn(`API for getInitialAssessmentQuestions returned non-array data for topic ${knowledgePointId}.`, data);
            return [];
        }
        
        return data.map((q: any) => ({ ...q, type: getQuestionTypeFromData(q) }));

    } catch (error) {
        console.error(`API call to getInitialAssessmentQuestions for topic ${knowledgePointId} failed.`, error);
        throw error;
    }
};

export const queryQuestionAndScoreReduce = async (userId: string, archetypeId: string): Promise<Question[]> => {
    const url = new URL('api/v1/questions/QueryquestionAndScoreReduce', API_BASE_URL);
    const data = await fetchApi(url.toString(), {
        method: 'POST',
        body: JSON.stringify({ userId, archetypeId: parseInt(archetypeId, 10) }) // API expects integer
    });
    if (Array.isArray(data)) {
        return data.map((q: any) => ({ ...q, type: getQuestionTypeFromData(q) }));
    }
    return [];
};

export const getRandomQuestionForArchetype = async (archetypeId: string, knowledgePointId?: string): Promise<Question[]> => {
    const url = new URL('api/v1/questions/questionQuery', API_BASE_URL);
    url.searchParams.append('archetypeId', archetypeId);
    if (knowledgePointId) {
        url.searchParams.append('knowledgePointId', knowledgePointId);
    }
    const data = await fetchApi(url.toString(), { method: 'GET' });
     if (Array.isArray(data)) {
        return data.map((q: any) => ({ ...q, type: getQuestionTypeFromData(q) }));
    }
    return [];
};

export const getArchetypeDetails = async (archetypeId: string): Promise<ProblemArchetypeDetails> => {
    const url = new URL('ProblemArchetype', API_BASE_URL);
    url.searchParams.append('archetypeId', archetypeId);
    return await fetchApi(url.toString(), { method: 'GET' });
};


export const getQuestionsForTraining = async (topicId: string, level: number): Promise<Question[]> => {
    // This function can remain for other potential uses, but the new flow uses different APIs.
    // Mocking a response for now.
    console.warn("getQuestionsForTraining is now a mock. The main flow uses other APIs.");
    return []; 
};

export const getQuestionsByKnowledgePoint = async (knowledgePointId: string): Promise<Question[]> => {
    // This new function handles the request from the KnowledgeMapPage
    // Assuming an endpoint, as the error implies its existence.
    const url = new URL('api/v1/questions/byKnowledgePoint', API_BASE_URL); 
    url.searchParams.append('knowledgePointId', knowledgePointId);
    const data = await fetchApi(url.toString(), { method: 'GET' });
    
    if (Array.isArray(data)) {
        return data.map((q: any) => ({
            ...q,
            type: getQuestionTypeFromData(q)
        }));
    }
    return [];
};


export const addScoreForCorrectAnswer = (userId: string, archetypeId: string): Promise<string> => {
    return fetchApi(`${API_BASE_URL}api/v1/questions/scoresAdd`, {
        method: 'POST',
        body: JSON.stringify({ userId, archetypeId }),
    });
};

export const getProficiencyPercentage = (userId: string, archetypeKeyword: string): Promise<string> => {
    const url = `${API_BASE_URL}api/v1/questions/proficiencyPercentage`;
    const body = {
        uid: userId,
        archetypekeyword: archetypeKeyword,
    };
    return fetchApi(url, { 
        method: 'POST', 
        body: JSON.stringify(body) 
    });
};


// --- Gemini API Services ---

export const preprocessAndValidateQuestion = async (question: Question): Promise<Question> => {
    console.log("AI_SVC: Pre-processing question ID:", question.id);
    if (!ai) {
        console.warn("Gemini AI not initialized. Returning raw question.");
        return question;
    }

    // A simplified schema focusing on the text parts that need cleaning.
    // The AI will be instructed to return the other fields as-is.
    const responseSchema = {
        type: Type.OBJECT,
        properties: {
            questionText: { type: Type.STRING },
            options: { type: Type.ARRAY, items: { type: Type.STRING } },
            standardAnswer: { type: Type.STRING },
            detailedSolution: { type: Type.STRING },
            keyTakeaways: { type: Type.STRING, nullable: true },
        },
    };

    const prompt = `You are a meticulous 考研数学 (Chinese Graduate School Entrance Examination for Mathematics) expert and question validator. Your task is to receive a question object in JSON format, thoroughly review and correct it, and return a perfectly formatted and validated JSON object.

**Instructions:**
1.  **Review all text fields:** Examine \`questionText\`, \`options\` (if any), \`standardAnswer\`, and \`detailedSolution\`.
2.  **Standardize LaTeX:** Correct any non-standard or shorthand LaTeX. Ensure all mathematical expressions are enclosed in single dollar signs for inline math (e.g., \`$x^2+1$\`).
    *   \`lim x->0\` or \`lim_{x->0}\` must become \`\\lim_{x \\to 0}\`.
    *   \`infty\` must become \`\\infty\`.
    *   \`sqrt(...)\` must become \`\\sqrt{...}\`.
    *   Fractions like \`(a+b)/(c+d)\` must become \`\\frac{a+b}{c+d}\`.
    *   Remove any invalid commands like \`\\color(red)\` or \`\\colorred\`.
    *   Fix any brace mismatches or other syntax errors.
3.  **Validate Logic:** This is the most critical step. Scrutinize the \`detailedSolution\`. Verify that every step logically follows and that the final result of the solution matches the \`standardAnswer\`.
4.  **Correct if Necessary:** If you find any logical errors in the \`detailedSolution\` or if it doesn't lead to the \`standardAnswer\`, you MUST correct both the \`detailedSolution\` and the \`standardAnswer\` to be accurate.
5.  **Format Output:** Return ONLY the corrected JSON object, strictly adhering to the provided JSON schema. Do not include any explanatory text, markdown formatting, or anything outside the JSON object.

Here is the question object to process:
\`\`\`json
${JSON.stringify({
    questionText: question.questionText,
    options: question.options,
    standardAnswer: question.standardAnswer,
    detailedSolution: question.detailedSolution,
    keyTakeaways: question.keyTakeaways,
})}
\`\`\``;

    try {
        const response = await ai.models.generateContent({
            model: TEXT_MODEL,
            contents: prompt,
            config: {
                responseMimeType: "application/json",
                responseSchema: responseSchema
            }
        });

        const cleanedData = JSON.parse(response.text);
        console.log("AI_SVC: Pre-processing successful.");
        return { ...question, ...cleanedData }; // Merge cleaned text back into original question object
    } catch (error) {
        console.error("Error during AI question preprocessing:", error);
        console.log("AI_SVC: Pre-processing failed. Returning raw question.");
        return question; // Fallback to original question on error
    }
};


export const getQuestionDetailsFromDB = async (questionId: string): Promise<Pick<Question, 'detailedSolution' | 'standardAnswer'>> => {
    console.log("DB_SIM: Fetching solution for questionId:", questionId);
    const question = await fetchApi(`${API_BASE_URL}api/v1/questions/${questionId}`);
    await new Promise(resolve => setTimeout(resolve, 300));

    if (question) {
        return { detailedSolution: question.detailedSolution, standardAnswer: question.standardAnswer };
    }
    throw new Error("Solution not found for this question.");
};

export const getAIHintForProblem = async (problemText: string): Promise<string> => {
  console.log("apiService: Fetching AI hint for problem:", problemText);
  if (!ai) {
    console.warn("Gemini AI not initialized. Returning mock hint.");
    await new Promise(resolve => setTimeout(resolve, 800));
    return `**[Mock AI Hint]** 思考一下这个极限是属于哪种未定式类型？例如，是 $\\frac{0}{0}$ 型还是 $1^{\\infty}$ 型？`;
  }

  try {
    const prompt = `You are a concise math tutor. A student is stuck on a problem. Provide a single, short, guiding hint in Chinese to help them start.
    Do not solve the problem or explain the full theory. Just give a nudge in the right direction.
    For example: "这是一个 0/0 型极限，考虑使用洛必达法则。" or "尝试对表达式进行通分。".
    Use LaTeX for math expressions like $x^2$.

    The problem is:
    "${problemText}"`;

    const response: GenerateContentResponse = await ai.models.generateContent({
      model: TEXT_MODEL,
      contents: prompt,
      config: { thinkingConfig: { thinkingBudget: 0 } }
    });

    return response.text;

  } catch (error) {
    console.error("Error getting AI hint from Gemini:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return `抱歉，AI提示服务暂时无法连接。错误: ${errorMessage}`;
  }
};


export const getAIExplanationForStep = async (problemText: string, solutionStep: string): Promise<string> => {
  console.log("apiService: Fetching AI explanation for step:", solutionStep);
  if (!ai) {
    console.warn("Gemini AI not initialized. Returning mock explanation.");
    await new Promise(resolve => setTimeout(resolve, 1200));
    return `**[Mock AI Hint]** 您问到了 **“${solutionStep}”**。这是一个很好的问题！你觉得这一步转换，是基于什么数学原理呢？是代数变形，还是应用了某个重要的极限理论？`;
  }

  try {
    const prompt = `You are a Socratic math tutor named "AI助教". Your goal is to guide the student to understand, not to give them the answer directly. The student is in China, studying for the graduate school entrance exam. A student is confused about a specific part of a solution.
    
    **Original Problem:** "${problemText}"
    
    **The part they are confused about is:** "${solutionStep}"
    
    **Your Task:**
    1.  Acknowledge their question.
    2.  **Do NOT explain the step directly.**
    3.  Instead, ask a guiding question that prompts them to think about the underlying principle, theorem, or next logical thought process. Lead them towards the answer without giving it away.
    4.  Keep your tone encouraging, patient, and conversational.
    5.  Respond in Chinese. Use LaTeX for math expressions (e.g., $x^2$).

    **Example Interaction:**
    *   Student asks about: "$\\sin(x) = x - \\frac{x^{3}}{6} + o(x^{3})$"
    *   Your response should be something like: "问得好！我们看到这里用一个多项式替换了$\\sin(x)$。你还记得在求 $x \\to 0$ 的极限时，有什么方法可以把复杂的函数用简单的多项式来近似吗？这通常和哪个重要的公式有关？"`;

    const response: GenerateContentResponse = await ai.models.generateContent({
      model: TEXT_MODEL,
      contents: prompt,
    });

    return response.text;

  } catch (error) {
    console.error("Error getting AI explanation from Gemini:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return `抱歉，AI解析服务暂时无法连接。请稍后重试。\n错误详情: ${errorMessage}`;
  }
};


export const getAITopicAnalysisReport = async (request: TopicAnalysisRequest): Promise<string> => {
    const { topicName, userAccuracy, systemAccuracy, weakArchetypes } = request;
    console.log("apiService: Generating AI analysis report for topic:", topicName);
    
    if (!ai) {
        console.warn("Gemini AI not initialized. Returning mock analysis report.");
        await new Promise(resolve => setTimeout(resolve, 1500));
        const weakPointsText = weakArchetypes.map(a => `*   **${a.name}**: 这类题型需要特别关注，你的表现似乎不太稳定。`).join('\n');
        return `### **专题分析报告: ${topicName}**

同学你好！这是你关于 **${topicName}** 专题的智能分析报告：

#### **核心数据对比**
*   你的专题正确率: **<span style="color: #16A34A; font-weight: bold;">${userAccuracy}%</span>**
*   全站平均正确率: **${systemAccuracy}%**

**评价:** 干得不错！你的正确率${userAccuracy > systemAccuracy ? '高于' : '接近'}平台平均水平，这表明你对该专题有较好的整体理解。

#### **薄弱环节诊断**
根据你的练习记录，以下题型是你需要特别加强的“软肋”：
${weakPointsText || '*   暂未发现明显的薄弱环节，请继续保持！'}

#### **学习建议**
1.  **精准打击**: 针对上述薄弱题型，建议进行专项练习，并仔细阅读相关知识点的解析。
2.  **复习回顾**: 定期回顾该专题的核心概念和公式，防止遗忘。
3.  **挑战难题**: 尝试解决更复杂的综合题，将知识融会贯通。

继续努力，你一定能完全掌握 **${topicName}**！`;
    }
    
    try {
        const weakPointsText = weakArchetypes.map(a => `* ${a.category}: ${a.name}`).join('\n');
        const prompt = `You are a data-driven learning coach for a university-level math student in China.
        The user wants an analysis report for the topic "${topicName}".
        Here is the data:
        - User's accuracy for this topic: ${userAccuracy}%
        - Platform average accuracy for this topic: ${systemAccuracy}%
        - The user's identified weak problem archetypes in this topic are:
        ${weakPointsText || "None"}

        Generate a concise, encouraging analysis report in Chinese. The report should:
        1. Start with a title for the report (e.g., "专题分析报告: [Topic Name]").
        2. Compare the user's accuracy with the system average and give a brief, positive evaluation.
        3. Clearly list the identified weak points under a "薄弱环节诊断" heading. If none, state that they are doing well.
        4. Provide 2-3 actionable learning suggestions under a "学习建议" heading.
        5. The tone should be supportive and professional.
        6. Use Markdown for formatting (headings, bold, lists). Use LaTeX for math expressions if needed.
        `;

        const response: GenerateContentResponse = await ai.models.generateContent({
            model: TEXT_MODEL,
            contents: prompt,
        });

        return response.text;

    } catch (error) {
        console.error("Error generating AI analysis report:", error);
        return `抱歉，AI分析报告生成失败。错误: ${error instanceof Error ? error.message : String(error)}`;
    }
};


export const getKnowledgeFramework = async (topic: string): Promise<KnowledgePoint[]> => {
  console.log("apiService: Fetching knowledge framework for topic:", topic);
  if (!ai) {
    console.warn("Gemini AI not initialized. Returning mock framework.");
    await new Promise(resolve => setTimeout(resolve, 800));
    return [
      { id: 'gen_kp1', title: `[Mock] 核心概念: ${topic}`, content: `关于 ${topic} 的关键定义和性质 (模拟数据)。` },
      { 
        id: 'gen_kp2', 
        title: `[Mock] 重要公式与定理: ${topic}`, 
        children: [
          { id: 'gen_kp2.1', title: '模拟公式 A', content: '牛顿-莱布尼茨公式: $$\\int_a^b f(x) dx = F(b) - F(a)$$'},
          { id: 'gen_kp2.2', title: '模拟定理 B', content: '介值定理内容简述 (模拟数据)'}
        ]
      }
    ];
  }

  try {
     const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: `Generate a hierarchical knowledge framework for the math topic: "${topic}".`,
        config: {
            responseMimeType: "application/json",
            responseSchema: {
                type: Type.ARRAY,
                items: {
                    type: Type.OBJECT,
                    properties: {
                        id: { type: Type.STRING },
                        title: { type: Type.STRING },
                        content: { type: Type.STRING },
                        children: {
                           type: Type.ARRAY,
                           items: {
                               type: Type.OBJECT,
                               properties: {
                                   id: { type: Type.STRING },
                                   title: { type: Type.STRING },
                                   content: { type: Type.STRING },
                               }
                           }
                        }
                    }
                }
            }
        }
    });

    const parsedData = JSON.parse(response.text);
    return parsedData as KnowledgePoint[];

  } catch (error)     {
     console.error("Error fetching knowledge framework from Gemini:", error);
     return [{id: 'err', title: `Error fetching framework: ${error instanceof Error ? error.message : String(error)}`}];
  }
};