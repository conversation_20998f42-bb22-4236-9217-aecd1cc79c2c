import React from 'react';

const HandRaisedIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.042 21.672L13.684 16.6m0 0l-2.51 2.225.569-9.47 5.227 7.917-3.286-.672ZM12 2.25V4.5m5.834.166l-1.591 1.591M20.25 10.5H18M17.834 14.834l-1.591-1.591M12 18.75V21.75M4.166 14.834l1.591-1.591M3.75 10.5H6m4.166-5.834l1.591 1.591" />
  </svg>
);

export default HandRaisedIcon;