<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考研数学智能伴学</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --color-primary: #409EFF; /* Tech Blue */
            --color-primary-light: #e6f2ff;
            --color-primary-dark: #3a8ee6;
            --color-secondary-text: #909399; /* Medium Gray */
            --color-border: #E4E7ED; /* Light Gray Border */
            --color-success: #67C23A;
            --color-warning: #E6A23C; /* Bright Orange */
            --color-danger: #F56C6C;
            --color-light-bg: #F8F9FA; /* Very Light Gray BG - Updated */
            --color-dark-text: #303133; /* Dark Gray Text */
        }

        body { 
            margin: 0; 
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            background-color: var(--color-light-bg);
        }
        .prose {
             --tw-prose-body: var(--color-dark-text);
             --tw-prose-headings: var(--color-dark-text);
             --tw-prose-lead: #4b5563;
             --tw-prose-links: var(--color-primary);
             --tw-prose-bold: var(--color-dark-text);
             --tw-prose-counters: var(--color-secondary-text);
             --tw-prose-bullets: #d1d5db;
             --tw-prose-hr: var(--color-border);
             --tw-prose-quotes: var(--color-dark-text);
             --tw-prose-quote-borders: var(--color-border);
             --tw-prose-captions: var(--color-secondary-text);
             --tw-prose-code: var(--color-dark-text);
             --tw-prose-pre-code: #e5e7eb;
             --tw-prose-pre-bg: #1f2937;
        }
        .prose code::before, .prose code::after {
            content: "" !important;
        }
        
        .auth-background {
            background-color: #e0e8f0;
            background-image: 
                radial-gradient(at 20% 15%, hsla(210, 80%, 70%, 0.2) 0px, transparent 50%),
                radial-gradient(at 80% 20%, hsla(280, 75%, 75%, 0.2) 0px, transparent 50%),
                radial-gradient(at 25% 85%, hsla(30, 70%, 70%, 0.25) 0px, transparent 50%),
                radial-gradient(at 75% 90%, hsla(180, 70%, 65%, 0.2) 0px, transparent 50%);
        }

        /* Animation Keyframes */
        @keyframes slide-in-from-right {
            from {
                transform: translateX(100%);
                opacity: 0.8;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        .animate-slide-in-from-right {
            animation: slide-in-from-right 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in-fast {
            animation: fadeIn 0.3s ease-out forwards;
        }

        @keyframes character-reveal {
            from {
                opacity: 0;
                transform: translateY(15px) skewY(6deg);
            }
            to {
                opacity: 1;
                transform: translateY(0) skewY(0deg);
            }
        }
        .animate-character-reveal {
            display: inline-block; /* Required for transform */
            opacity: 0;
            animation: character-reveal 0.6s cubic-bezier(0.2, 0.8, 0.2, 1) forwards;
        }
        
        @keyframes pulse-slow {
            50% {
                opacity: .8;
            }
        }
        .animate-pulse-slow {
            animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
    </style>
<script>
  window.MathJax = {
    tex: {
      packages: {'[+]': ['ams', 'color']},
      inlineMath: [['$', '$'], ['\\(', '\\)']],
      displayMath: [['$$', '$$'], ['\\[', '\\]']],
      processEscapes: true,
      macros: {
        lim: "\\mathop{\\mathrm{lim}}\\limits"
      }
    },
    svg: {
      fontCache: 'global'
    },
    startup: {
      // We'll trigger rendering manually in React components, so disable initial typesetting
      typesetOnLoad: false,
      ready: () => {
        MathJax.startup.defaultReady();
        // Dispatch a custom event to signal that MathJax is fully loaded and ready.
        document.dispatchEvent(new Event('mathjax-ready'));
      }
    }
  };
</script>
<script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-svg.js"></script>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@heroicons/react/": "https://esm.sh/@heroicons/react@^2.2.0/",
    "recharts": "https://esm.sh/recharts@^3.1.0",
    "@google/genai": "https://esm.sh/@google/genai@^1.9.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body>
    <div id="root"></div>
    <!-- 
      Developer Note: 
      For Gemini API integration, ensure the API_KEY environment variable (process.env.API_KEY) 
      is properly configured in your deployment environment. 
      The application will use this key to communicate with Google's Generative AI services.
      Mock data is used if the API key is not found.
    -->
    <script type="module" src="./index.tsx"></script> 
<script type="module" src="/index.tsx"></script>
</body>
</html>