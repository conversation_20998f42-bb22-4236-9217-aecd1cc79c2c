

import { SubjectTopic, HistoryItem, KnowledgePoint, Question, SubjectId, SubjectDisplayName, ProblemArchetype, TopicLevel, CoreConceptFlowchart, ActivePage, Prerequisite, Exam, QuestionType, ReferenceInfo } from './types';

// --- START: Raw Data from User ---
const TOPICS_RAW = `
101,函数极限
102,函数·微专题 ([函数极限] 附赠)
103,数列极限
104,连续、间断与导数
105,中值定理
106,导数应用
107,导数证明
108,积分1
109,积分2
110,定积分应用
111,多元微分概念
112,多元微分计算
113,重积分
114,微分方程
115,曲线积分
116,曲面积分
117,级数判敛
118,幂级数
`;

const PROBLEM_ARCHETYPES_RAW = `
1,101,基础知识,理解函数极限的定义（ε-δ）、性质、运算法则及两个重要极限的应用。
2,101,解题技巧,熟练运用等价无穷小替换、洛必达法则、泰勒公式等核心技巧求解各类未定式极限。
3,101,综合应用,运用函数极限判断函数的连续性、寻找函数的渐近线、或解决与导数和积分定义相关的问题。
4,101,复杂题型,求解含参变量的极限、变限积分的极限，或分析分段函数的极限。
5,103,基础知识,理解数列极限的定义、性质及收敛的必要条件。
6,103,解题技巧,掌握利用单调有界准则和夹逼准则判断数列极限收敛性及求极限。
7,103,综合应用,将数列极限与函数极限进行转化，或应用于级数收敛性的判断。
8,103,复杂题型,处理由递推关系定义的数列的极限问题。
9,104,基础知识,理解函数连续性的定义，判断间断点类型；掌握导数和微分的定义、几何意义及求导法则。
10,104,解题技巧,熟练进行复合函数、隐函数、参数方程的求导，并利用连续性定义确定分段函数中的参数。
11,104,综合应用,探讨闭区间连续函数的性质（零点、介值定理），并利用导数定义求解特定点的导数值。
12,104,复杂题型,分析由抽象函数或级数定义的函数的连续性与可导性问题。
13,105,基础知识,深刻理解并能准确叙述罗尔、拉格朗日、柯西中值定理和泰勒定理。
14,105,解题技巧,掌握构造辅助函数以应用中值定理的常见方法，如作差、作商、积分还原等。
15,105,综合应用,利用中值定理证明等式、不等式，或判断方程根的存在性与个数。
16,105,复杂题型,在复杂的证明题中，需要多次使用中值定理或结合单调性、凹凸性等知识进行综合推理。
36,101,解题技巧,[解题技巧]: 泰勒展开与等价无穷小.
37,103,解题技巧,[解题技巧]: 周期函数的极限.
38,101,解题技巧,[解题技巧]: 分段函数的极限与参数求解.
39,101,解题技巧,[解题技巧]: 高阶无穷小定参.
41,103,解题技巧,[解题技巧]: 递推数列极限(进阶).
42,102,基础知识,掌握函数极限的特殊题型和技巧。
43,106,基础知识,掌握利用导数判断函数单调性、求极值与最值。
44,107,基础知识,掌握利用导数定义或中值定理进行证明。
45,108,基础知识,理解原函数与不定积分的概念，掌握基本积分公式。
46,109,基础知识,理解定积分的概念与性质，掌握牛顿-莱布尼茨公式。
47,110,基础知识,掌握利用定积分计算平面图形面积和旋转体体积。
48,111,基础知识,理解多元函数的概念、极限、连续性与偏导数。
49,112,基础知识,掌握多元函数求偏导数、全微分的方法。
50,113,基础知识,理解二重积分的概念与性质，掌握直角坐标与极坐标下的计算。
51,114,基础知识,掌握可分离变量、一阶线性等微分方程的解法。
52,115,基础知识,理解第一型与第二型曲线积分的概念与计算。
53,116,基础知识,理解第一型与第二型曲面积分的概念与计算。
54,117,基础知识,掌握正项级数、交错级数和任意项级数的收敛判别法。
55,118,基础知识,理解幂级数的收敛半径、收敛域和和函数。
`;
// --- END: Raw Data ---

const ALL_PREREQUISITES: Record<string, Prerequisite[]> = {
    '101': [
        { 
            id: 'pre_limit_def', 
            name: '函数极限的定义 $\\lim\\limits_{x \\to \\infty} f(x)$', 
            reference: {
                title: '请去这里学',
                items: [
                    '1. 参考同济高数第七版 (上册) 的下列部分',
                    ' • P27 "1 自变量趋于有限值时函数的极限" 前两段',
                    ' • P28 定义1',
                    ' • P31 "2 自变量趋于无穷大时函数的极限" 第一段, 定义2'
                ]
            }
        },
        { id: 'pre_infinitesimal_def', name: '无穷小的定义 $\\lim f(x) = 0$' },
        { id: 'pre_infinity_def', name: '无穷大的定义 $\\lim f(x) = \\infty$' },
        { id: 'pre_infinitesimal_compare', name: '无穷小的比较: 高阶、低阶、同阶、k阶、等价无穷小' },
        { id: 'pre_common_infinitesimal', name: '常用等价无穷小' },
        { id: 'pre_taylor', name: '泰勒展开', reference: { title: '请去这里学', items: ['请参考教材中关于泰勒公式的章节。'] } },
        { id: 'pre_common_taylor', name: '常用的泰勒展开' },
        { id: 'pre_lopital', name: '洛必达法则' },
        { id: 'pre_basic_derivatives', name: '基本求导法则与求导公式 (总表)' },
        { id: 'pre_product_quotient_rule', name: '导数和差积商' },
        { id: 'pre_chain_rule', name: '链式求导法则' },
        { id: 'pre_definite_integral_def', name: '定积分定义 $\\int_a^b f(x)dx$' },
    ]
};

// --- Data Parsing ---
const parseProblemArchetypes = (raw: string): ProblemArchetype[] => {
    return raw.trim().split('\n').map((line, index) => {
        const [id, topicId, category, ...descriptionParts] = line.split(',');
        const description = descriptionParts.join(',').trim();
        let name = description;
        if (description.startsWith('[')) {
            const match = description.match(/\[.*?\]:\s*(.*)/);
            name = match ? match[1] : description;
        } else {
             name = description.split('，')[0].replace(/本题(型)?考察/, '').trim();
        }

        // Mock masteryStatus for Progress Page
        let masteryStatus: ProblemArchetype['masteryStatus'];
        const mod = index % 10;
        if (mod < 2) masteryStatus = 'needs_review';
        else if (mod < 5) masteryStatus = 'needs_observation';
        else masteryStatus = 'good_mastery';
        
        // Mock passRate and difficulty
        let difficulty: ProblemArchetype['difficulty'];
        if (category === '基础知识') difficulty = '简单';
        else if (category === '解题技巧') difficulty = '中等';
        else difficulty = '困难';

        const passRate = 50 + (name.length % 40); // Simple mock pass rate

        return {
            id: id.trim(),
            topicId: topicId.trim(),
            category: category.trim() as any,
            name: name,
            description: description,
            masteryStatus: masteryStatus,
            passRate: passRate,
            difficulty: difficulty,
        };
    });
};

const parseTopics = (raw: string): Omit<SubjectTopic, 'problemArchetypes' | 'subjectId'>[] => {
    return raw.trim().split('\n').map(line => {
        const [id, fullName] = line.split(',');
        const name = fullName.trim();
        const subtitleMatch = name.match(/^(.*?)\s+\((.*)\)$/);
        
        if (subtitleMatch && subtitleMatch[1] && subtitleMatch[2]) {
            return {
                id: id.trim(),
                name: subtitleMatch[1].trim(),
                subtitle: `(${subtitleMatch[2].trim()})`,
            };
        }

        return {
            id: id.trim(),
            name: name,
        };
    });
}

export const ALL_PROBLEM_ARCHETYPES = parseProblemArchetypes(PROBLEM_ARCHETYPES_RAW);
const ALL_TOPICS_PARSED = parseTopics(TOPICS_RAW);

// --- Structured Data for App ---
export const LEVELS: Record<string, TopicLevel[]> = {
    '101': [
        { level: 1, name: "基础知识", description: "掌握函数极限的核心定义、性质和基本计算。", archetypeIds: ['1'] },
        { level: 2, name: "核心技巧", description: "熟练运用洛必达、泰勒公式等解决未定式。", archetypeIds: ['2', '36', '38', '39'] },
        { level: 3, name: "综合与复杂题型", description: "处理含参、变限积分等复杂极限问题。", archetypeIds: ['3', '4'] },
    ],
    '103': [
        { level: 1, name: "基础知识", description: "掌握数列极限的核心定义与性质。", archetypeIds: ['5'] },
        { level: 2, name: "核心技巧", description: "熟练运用单调有界和夹逼准则。", archetypeIds: ['6', '37', '41'] },
    ]
}

export const CORE_CONCEPT_FLOWCHARTS: Record<string, CoreConceptFlowchart> = {
    '101': {
        nodes: [
            { id: 'start', label: '函数的极限', type: 'start'},
            { id: 'type1', label: '类型一\n$\\frac{0}{0}, \\frac{\\infty}{\\infty}, 0 \\cdot \\infty$', type: 'decision' },
            { id: 'type2', label: '类型二\n$\\infty - \\infty$', type: 'decision' },
            { id: 'type3a', label: '类型三 (a)\n$1^{\\infty}$', type: 'decision' },
            { id: 'type3b', label: '类型三 (b)\n$0^{0}, \\infty^{0}$', type: 'decision' },
            { id: 'method1', label: '按顺序考虑最简单:\n(1) 等价无穷小\n(2) 泰勒公式\n(3) 洛必达法则', type: 'process' },
            { id: 'method2', label: '通分或乘除某式化为类型一', type: 'process' },
            { id: 'method3a', label: '运用重要极限 $\\lim\\limits_{\\alpha \\to 0}(1+\\alpha)^{\\frac{1}{\\alpha}}=e$', type: 'process' },
            { id: 'method3b', label: '写成 $e^{g(x)\\ln f(x)}$ 形式, 化为类型一', type: 'process' },
        ],
        edges: [
            { from: 'start', to: 'type1' },
            { from: 'start', to: 'type2' },
            { from: 'start', to: 'type3a' },
            { from: 'start', to: 'type3b' },
            { from: 'type1', to: 'method1' },
            { from: 'type2', to: 'method2' },
            { from: 'type3a', to: 'method3a' },
            { from: 'type3b', to: 'method3b' },
        ]
    }
}


const buildTrainingTopics = (): Record<SubjectId, SubjectTopic[]> => {
    const higherMathTopics = ALL_TOPICS_PARSED.map(topicData => {
        const archetypesForTopic = ALL_PROBLEM_ARCHETYPES.filter(p => p.topicId === topicData.id);
        const levelsForTopic = LEVELS[topicData.id] || [];
        const flowchartForTopic = CORE_CONCEPT_FLOWCHARTS[topicData.id];
        
        return {
            ...topicData,
            subjectId: SubjectId.HIGHER_MATH,
            problemArchetypes: archetypesForTopic,
            levels: levelsForTopic,
            flowchart: flowchartForTopic,
            prerequisites: ALL_PREREQUISITES[topicData.id] || [{id: 'gen_pre', name: `掌握 ${topicData.name} 的基本概念`}]
        };
    });
    
    return {
        [SubjectId.HIGHER_MATH]: higherMathTopics,
        [SubjectId.LINEAR_ALGEBRA]: [],
        [SubjectId.PROBABILITY_STATS]: [],
    };
};

export const TRAINING_TOPICS: Record<SubjectId, SubjectTopic[]> = buildTrainingTopics();
export const APP_TITLE_SUFFIX = '智能伴学';
export const DEFAULT_USER = { id: 'mock_user', username: "吴二狗", avatarUrl: "https://picsum.photos/seed/user/40/40" };
export const AVAILABLE_SUBJECTS = [
  { id: SubjectId.HIGHER_MATH, name: SubjectDisplayName[SubjectId.HIGHER_MATH] },
  { id: SubjectId.LINEAR_ALGEBRA, name: SubjectDisplayName[SubjectId.LINEAR_ALGEBRA], disabled: true },
  { id: SubjectId.PROBABILITY_STATS, name: SubjectDisplayName[SubjectId.PROBABILITY_STATS], disabled: true },
];
export const NAV_ITEMS: ActivePage[] = [ActivePage.AssessmentCenter, ActivePage.Competition, ActivePage.History, ActivePage.KnowledgeMap];

export const SUBJECT_KNOWLEDGE_MAP_DATA: Record<SubjectId, KnowledgePoint[]> = {
  [SubjectId.HIGHER_MATH]: [
    {
      id: 'kp_hm_ch1',
      title: '第一章 函数、极限与连续',
      subjectId: SubjectId.HIGHER_MATH,
      content: '本章是整个高等数学的基础，核心是极限思想。',
      children: [
        {
          id: 'kp_hm_ch1_1',
          title: '函数',
          content: '主要内容包括函数的定义、表示法、几种重要函数（复合函数、反函数、初等函数）以及函数的性质（单调性、奇偶性、周期性、有界性）。'
        },
        {
          id: '101',
          title: '极限',
          content: '核心概念。包括数列极限和函数极限的定义（$\\epsilon-N$, $\\epsilon-\\delta$），性质，运算法则，两个重要极限 ($\\lim\\limits_{x \\to 0} \\frac{\\sin x}{x} = 1$ 和 $\\lim\\limits_{x \\to \\infty} (1+\\frac{1}{x})^{x} = e$) 以及无穷小与无穷大。',
          children: [
            { id: 'kp_hm_ch1_2_1', title: '极限计算方法', content: '四则运算、洛必达法则、泰勒公式、等价无穷小替换、夹逼准则、单调有界准则等。' }
          ]
        },
        {
          id: 'kp_hm_ch1_3',
          title: '连续',
          content: '函数连续性的定义、间断点的分类、闭区间上连续函数的性质（有界性、最值定理、零点定理、介值定理）。'
        }
      ]
    },
    {
      id: 'kp_hm_ch2',
      title: '第二章 导数与微分',
      subjectId: SubjectId.HIGHER_MATH,
      content: '本章研究函数的变化率问题，引入了导数和微分的概念。',
      children: []
    }
  ],
  [SubjectId.LINEAR_ALGEBRA]: [],
  [SubjectId.PROBABILITY_STATS]: [],
};


export const MOCK_HISTORY_ITEMS: HistoryItem[] = [
  { id: '1', type: '测试', title: '完成[函数极限] 等级1测试', date: '2023年6月25日', subjectId: SubjectId.HIGHER_MATH, details: '用时: 15:32, 正确率: 80%' },
  { id: '4', type: '推荐测评', title: '高等数学综合能力测评', date: '2023年6月26日', subjectId: SubjectId.HIGHER_MATH, details: '系统推荐，检验近期学习成果' },
  { id: '3', type: '错题订正', title: '洛必达法则应用错误 (高数)', date: '2023年6月23日', subjectId: SubjectId.HIGHER_MATH },
];


// MOCK_QUESTIONS is removed, as questions will be fetched from API.
// We keep MOCK_WEEKLY_EXAM because there is no API endpoint for exams.
// We need some questions for it, so we'll define them here locally.
const examQuestionsForMock: Question[] = [
   { 
    id: 'exam_q_1', 
    archetypeId: '2',
    topicId: '101',
    type: '单选题',
    questionText: '求极限 $\\lim\\limits_{x \\to 0} \\frac{e^x - e^{-x} - 2x}{x - \\sin x}$ 的值。', 
    options: ['1', '2', '-1', '-2'],
    standardAnswer: 'B',
    detailedSolution: '此为 $\\frac{0}{0}$ 型未定式。我们使用洛必达法则。\n\n**第一次求导:**\n分子: $(e^x - e^{-x} - 2x)\' = e^x + e^{-x} - 2$\n分母: $(x - \\sin x)\' = 1 - \\cos x$\n原极限 $= \\lim\\limits_{x \\to 0} \\frac{e^x + e^{-x} - 2}{1 - \\cos x}$\n\n**第二次求导:** (仍为 $\\frac{0}{0}$ 型)\n分子: $(e^x + e^{-x} - 2)\' = e^x - e^{-x}$\n分母: $(1 - \\cos x)\' = \\sin x$\n原极限 $= \\lim\\limits_{x \\to 0} \\frac{e^x - e^{-x}}{\\sin x}$\n\n**第三次求导:** (仍为 $\\frac{0}{0}$ 型)\n分子: $(e^x - e^{-x})\' = e^x + e^{-x}$\n分母: $(\\sin x)\' = \\cos x$\n原极限 $= \\lim\\limits_{x \\to 0} \\frac{e^x + e^{-x}}{\\cos x} = \\frac{1+1}{1} = 2$\n\n所以选 B。'
  },
  { 
    id: 'exam_q_2', 
    archetypeId: '2',
    topicId: '101',
    type: '单选题',
    questionText: '求极限 $\\lim\\limits_{x \\to 0} (1+2\\sin x)^{\\frac{1}{x}}$ 的值。',
    options: ['$e^2$', '$e$', '$1$', '$e^{-1}$'],
    standardAnswer: 'A',
    detailedSolution: '此为 $1^\\infty$ 型未定式，利用重要极限公式 $e^{\\lim_{x \\to a} [f(x)-1]g(x)}$ 求解。\n设 $f(x) = 1+2\\sin x$, $g(x) = \\frac{1}{x}$。\n原极限 $= e^{\\lim\\limits_{x \\to 0} (1+2\\sin x - 1) \\cdot \\frac{1}{x}}$\n$= e^{\\lim\\limits_{x \\to 0} \\frac{2\\sin x}{x}}$\n根据重要极限 $\\lim\\limits_{x \\to 0} \\frac{\\sin x}{x} = 1$，\n$= e^{2 \\cdot 1} = e^2$。\n所以选 A。'
  },
   { 
    id: 'exam_q_3', 
    archetypeId: '1',
    topicId: '101',
    type: '单选题',
    questionText: '求极限 $\\lim\\limits_{x \\to \\infty} x \\sin(\\frac{1}{x})$ 的值是？',
    options: ['0', '1', '$\\infty$', '不存在'],
    standardAnswer: 'B',
    detailedSolution: '令 $t = \\frac{1}{x}$。当 $x \\to \\infty$ 时，$t \\to 0$。\n原极限 $= \\lim\\limits_{t \\to 0} \\frac{1}{t} \\sin(t) = \\lim\\limits_{t \\to 0} \\frac{\\sin t}{t}$。\n根据重要极限，$\\lim\\limits_{t \\to 0} \\frac{\\sin t}{t} = 1$。\n所以选 B。'
  },
    { 
    id: 'exam_q_4', 
    archetypeId: '36',
    topicId: '101',
    type: '计算题',
    questionText: '求极限 $\\lim\\limits_{x \\to 0} \\frac{x - \\arctan x}{\\sin^3 x}$', 
    options: null,
    standardAnswer: '1/3',
    detailedSolution: '当 $x \\to 0$ 时，$\\sin x \\sim x$，所以 $\\sin^3 x \\sim x^3$。\n原极限 $= \\lim\\limits_{x \\to 0} \\frac{x - \\arctan x}{x^3}$。\n这是一个 $\\frac{0}{0}$ 型极限。使用泰勒公式展开 $\\arctan x$。\n$\\arctan x = x - \\frac{x^3}{3} + o(x^3)$。\n代入原式: \n$= \\lim\\limits_{x \\to 0} \\frac{x - (x - \\frac{x^3}{3} + o(x^3))}{x^3} = \\lim\\limits_{x \\to 0} \\frac{\\frac{x^3}{3} - o(x^3)}{x^3}$\n$= \\frac{1}{3}$。'
  },
  { 
    id: 'exam_q_5', 
    archetypeId: '45',
    topicId: '108',
    type: '计算题',
    questionText: '计算不定积分 $\\int x \\cos(2x) dx$。', 
    options: null,
    standardAnswer: '$\\frac{1}{2}x\\sin(2x) + \\frac{1}{4}\\cos(2x) + C$',
    detailedSolution: '使用分部积分法 $\\int u dv = uv - \\int v du$。\n设 $u = x$, $dv = \\cos(2x) dx$。\n则 $du = dx$, $v = \\int \\cos(2x) dx = \\frac{1}{2}\\sin(2x)$。\n所以 $\\int x \\cos(2x) dx = x (\\frac{1}{2}\\sin(2x)) - \\int (\\frac{1}{2}\\sin(2x)) dx$\n$= \\frac{1}{2}x\\sin(2x) - \\frac{1}{2} \\int \\sin(2x) dx$\n$= \\frac{1}{2}x\\sin(2x) - \\frac{1}{2} (-\\frac{1}{2}\\cos(2x)) + C$\n$= \\frac{1}{2}x\\sin(2x) + \\frac{1}{4}\\cos(2x) + C$。'
  },
];


export const MOCK_WEEKLY_EXAM: Exam = {
    id: 'weekly_exam_2025_06_w2',
    name: '六月第二周周测',
    questions: examQuestionsForMock,
    totalTimeSeconds: 25 * 60, // 25 minutes
};