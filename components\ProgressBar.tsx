
import React from 'react';

interface ProgressSegment {
  percentage: number;
  color: string; // e.g., 'bg-progress-green'
}

interface ProgressBarProps {
  segments: ProgressSegment[];
  height?: string; // e.g., 'h-2.5'
}

const ProgressBar: React.FC<ProgressBarProps> = ({ segments, height = 'h-2.5' }) => {
  // Map base colors to rich gradients for better visual feedback
  const colorToGradientMap: Record<string, string> = {
    'bg-progress-green': 'bg-gradient-to-r from-green-400 to-green-500',
    'bg-primary-blue': 'bg-gradient-to-r from-blue-500 to-blue-600',
    'bg-blue-500': 'bg-gradient-to-r from-blue-400 to-blue-600',
    'bg-yellow-500': 'bg-gradient-to-r from-yellow-400 to-yellow-500',
    'bg-orange-400': 'bg-gradient-to-r from-orange-500 to-yellow-300', // Updated gradient
    'bg-red-500': 'bg-gradient-to-r from-red-500 to-red-600',
    'bg-purple-500': 'bg-gradient-to-r from-purple-500 to-purple-600',
    'bg-gray-400': 'bg-gradient-to-r from-gray-400 to-gray-500',
  };

  return (
    <div className={`w-full bg-gray-200 rounded-full ${height} dark:bg-gray-700 overflow-hidden flex`}>
      {segments.map((segment, index) => {
        const validPercentage = Math.max(0, Math.min(100, segment.percentage));
        // Use gradient if available, otherwise fall back to the provided color class
        const gradientColor = colorToGradientMap[segment.color] || segment.color;

        return (
          <div
            key={index}
            className={`${gradientColor} ${height} transition-all duration-500 ease-out`}
            style={{ width: `${validPercentage}%` }}
            aria-valuenow={validPercentage}
            aria-valuemin={0}
            aria-valuemax={100}
            role="progressbar"
            aria-label={`Segment ${index + 1}: ${validPercentage}%`}
          ></div>
        );
      })}
    </div>
  );
};

export default ProgressBar;
