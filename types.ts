export enum ActivePage {
  AssessmentCenter = '测评中心', 
  DataCenter = '数据中心',
  TrainingFocus = '专项突破',
  History = '数据总览',
  KnowledgeMap = '知识体系',
  Competition = '考试',
  ExamTaking = '考试中',
}

export enum SubjectId {
  HIGHER_MATH = 'higher_math',
  LINEAR_ALGEBRA = 'linear_algebra',
  PROBABILITY_STATS = 'probability_stats'
}

export const SubjectDisplayName: Record<SubjectId, string> = {
  [SubjectId.HIGHER_MATH]: '高等数学',
  [SubjectId.LINEAR_ALGEBRA]: '线性代数',
  [SubjectId.PROBABILITY_STATS]: '概率论与数理统计'
};

export interface User {
  id: string;
  username: string;
  couponBalance?: number;
  createdAt?: string;
  lastLoginAt?: string | null;
  avatarUrl?: string; // Keep for UI, can be a default
}

export type MasteryStatus = 'needs_review' | 'needs_observation' | 'good_mastery';

export interface ProblemArchetype {
  id: string;
  topicId: string;
  category: '基础知识' | '解题技巧' | '综合应用' | '复杂题型';
  name: string; 
  description: string;
  mastery?: number; 
  masteryStatus?: MasteryStatus;
  passRate?: number;
  difficulty?: '简单' | '中等' | '困难';
}

export interface ProblemArchetypeDetails {
    id: number;
    parentKpId: number;
    archetypeType: string;
    description: string;
}

export interface TopicLevel {
    level: number;
    name: string;
    description: string;
    archetypeIds: string[];
}

export interface CoreConceptNode {
    id:string;
    label: string;
    type: 'start' | 'process' | 'decision' | 'end';
}

export interface CoreConceptEdge {
    from: string;
    to: string;
    label?: string;
}

export interface CoreConceptFlowchart {
    nodes: CoreConceptNode[];
    edges: CoreConceptEdge[];
}

export interface ReferenceInfo {
  title: string;
  items: string[];
}

export interface Prerequisite {
  id: string;
  name: string;
  reference?: ReferenceInfo;
}

export interface SubjectTopic {
  id: string; 
  name:string; 
  subtitle?: string;
  subjectId?: SubjectId; 
  level?: number;
  progress?: number; 
  subTopics?: SubjectTopic[];
  problemArchetypes?: ProblemArchetype[]; 
  prerequisites?: Prerequisite[];
  levels?: TopicLevel[];
  flowchart?: CoreConceptFlowchart;
}


export interface HistoryItem {
  id: string;
  type: '任务' | '测试' | '错题订正' | '推荐测评';
  title: string;
  date: string;
  details?: string;
  subjectId?: SubjectId;
}

export interface KnowledgePoint {
  id: string;
  title: string;
  content?: string; 
  children?: KnowledgePoint[];
  subjectId?: SubjectId;
}

export type QuestionType = '单选题' | '多选题' | '填空题' | '计算题' | '简答题';


export interface Question {
  id: string;
  topicId: string;
  archetypeId: string;
  questionText: string;
  options: string[] | null;
  standardAnswer: string;
  detailedSolution: string;
  keyTakeaways?: string | null;
  imageUrl?: string | null;
  diagramDescription?: string | null;
  difficultyLevel?: number;
  computations?: boolean;
  verified?: boolean;
  isProcessedByAI?: boolean;
  sourceInfo?: {
      book?: string;
      page?: number;
  } | null;
  createdAt?: string;
  updatedAt?: string;
  // For UI consistency, we can derive this
  type: QuestionType;
}

export interface ArchetypeSession {
    topic: SubjectTopic;
    level: number;
    questionCount: number;
    totalTimeSeconds: number;
    questions?: Question[]; // Add optional questions array
}

export interface Exam {
    id: string;
    name: string;
    questions: Question[];
    totalTimeSeconds: number;
}

export type BreakthroughSessionState = { topicId: string; archetypeId: string } | null;

export interface TopicAnalysisRequest {
    topicName: string;
    userAccuracy: number;
    systemAccuracy: number;
    weakArchetypes: ProblemArchetype[];
}

// Added this type to fix an error in LineChart.tsx
export interface ProgressChartDataPoint {
  [key: string]: string | number;
}