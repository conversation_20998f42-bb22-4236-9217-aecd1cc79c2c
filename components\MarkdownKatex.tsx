
import React, { useEffect, useMemo, useRef, useState } from 'react';

// Define MathJax on the window object for TypeScript
declare global {
  interface Window {
    MathJax: {
      typesetPromise: (elements?: (HTMLElement | null)[]) => Promise<void>;
      startup?: {
        defaultReady: () => void;
      }
    };
  }
}

interface MathRendererProps {
  content: string;
  className?: string;
}

const MathRenderer: React.FC<MathRendererProps> = ({ content, className }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isMathJaxReady, setIsMathJaxReady] = useState(
    () => typeof window.MathJax?.typesetPromise === 'function'
  );

  useEffect(() => {
    if (isMathJaxReady) return;
    const handleReady = () => setIsMathJaxReady(true);
    document.addEventListener('mathjax-ready', handleReady);
    // Double-check if MathJ<PERSON> loaded before the component mounted
    if (typeof window.MathJax?.typesetPromise === 'function' && !isMathJaxReady) {
      setIsMathJaxReady(true);
    }
    return () => document.removeEventListener('mathjax-ready', handleReady);
  }, [isMathJaxReady]);
  
  const processedContent = useMemo(() => {
    if (!content) return null;
    
    const lines = content.split('\n');

    return lines.map((line, lineIndex) => {
      if (!line.trim()) {
        return <div key={lineIndex} className="h-4" />; // Preserve empty lines
      }
      
      // Split into text and math parts. The regex handles both inline ($...$) and display ($$...$$) math.
      // The AI pre-processing step has already ensured the format is correct.
      const parts = line.split(/(\$\$[\s\S]*?\$\$|\$.*?\$)/g).filter(Boolean);

      return (
        <div key={lineIndex} className="flex items-baseline flex-wrap leading-loose">
          {parts.map((part, partIndex) => {
            // No more manual preprocessing is needed here.
            // The AI service has already cleaned and formatted the math content.
            return <span key={partIndex}>{part}</span>;
          })}
        </div>
      );
    });
  }, [content]);

  useEffect(() => {
    // This is the simplified, correct way to use MathJax with React.
    if (isMathJaxReady && containerRef.current) {
      // Use a timeout to allow React to render the new content first
      setTimeout(() => {
        if (containerRef.current) {
             window.MathJax.typesetPromise([containerRef.current]).catch((err) =>
                console.error('MathJax typesetting error:', err)
            );
        }
      }, 0);
    }
  }, [processedContent, isMathJaxReady]); // Rerun when content changes

  // Render the processed JSX directly into the container.
  return (
    <div ref={containerRef} className={className}>
      {processedContent}
    </div>
  );
};

export default MathRenderer;