import React, { useState, useEffect, useMemo, FC } from 'react';
import { CheckIcon, EnvelopeIcon, KeyIcon, ShieldCheckIcon, ArrowPathIcon } from '@heroicons/react/24/solid';
import * as api from '../services/apiService';

interface RegistrationPageProps {
    onRegisterSuccess: () => void;
    onNavigateToLogin: () => void;
}

type Step = 'info' | 'success';

const ProgressStep: FC<{ number: number; label: string; isActive: boolean; isCompleted: boolean }> = ({ number, label, isActive, isCompleted }) => {
    const circleClasses = useMemo(() => {
        let base = 'w-8 h-8 rounded-full flex items-center justify-center border-2 transition-all duration-300 font-bold';
        if (isCompleted) {
            return `${base} bg-blue-600 border-blue-600 text-white`;
        }
        if (isActive) {
            return `${base} bg-white border-blue-600 text-blue-600 scale-110`;
        }
        return `${base} bg-gray-100 border-gray-300 text-gray-400`;
    }, [isActive, isCompleted]);

    const labelClasses = useMemo(() => {
        let base = 'mt-2 text-xs sm:text-sm font-semibold transition-colors duration-300 whitespace-nowrap';
        if (isActive || isCompleted) {
            return `${base} text-blue-700`;
        }
        return `${base} text-gray-500`;
    }, [isActive, isCompleted]);

    return (
        <div className="flex flex-col items-center">
            <div className={circleClasses}>
                {isCompleted ? <CheckIcon className="w-5 h-5" /> : number}
            </div>
            <p className={labelClasses}>{label}</p>
        </div>
    );
};

const ProgressIndicator: FC<{ currentStep: Step }> = ({ currentStep }) => {
    const steps = [
        { id: 'info' as Step, label: '填写信息' },
        { id: 'success' as Step, label: '完成注册' },
    ];
    const currentIndex = steps.findIndex(s => s.id === currentStep);

    return (
        <div className="w-full px-2 sm:px-4">
            <div className="flex items-center justify-center">
                {steps.map((step, index) => (
                    <React.Fragment key={step.id}>
                        <ProgressStep
                            number={index + 1}
                            label={step.label}
                            isActive={currentIndex === index}
                            isCompleted={currentIndex > index}
                        />
                        {index < steps.length - 1 && (
                            <div className={`flex-grow h-0.5 mx-2 sm:mx-4 rounded-full transition-colors ${currentIndex > index ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
                        )}
                    </React.Fragment>
                ))}
            </div>
        </div>
    );
};


const RegistrationPage: React.FC<RegistrationPageProps> = ({ onRegisterSuccess, onNavigateToLogin }) => {
    const [step, setStep] = useState<Step>('info');
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    
    useEffect(() => {
        if (step === 'success') {
            const timer = setTimeout(() => {
                onNavigateToLogin();
            }, 3000);
            return () => clearTimeout(timer);
        }
    }, [step, onNavigateToLogin]);

    const handleFinalRegister = async (e: React.FormEvent) => {
        e.preventDefault();
        setError('');
        if (!password || password.length < 6) {
            setError('密码长度不能少于6位。');
            return;
        }
        if (password !== confirmPassword) {
            setError('两次输入的密码不一致。');
            return;
        }
        
        setIsLoading(true);
        try {
            await api.register(username, password);
            setStep('success');
        } catch (err: any) {
            setError(err.message || '注册失败，请稍后重试。');
        } finally {
            setIsLoading(false);
        }
    };
    
    const renderContent = () => {
        switch (step) {
            case 'info':
                 return (
                    <form onSubmit={handleFinalRegister} className="space-y-5">
                         <div>
                            <label htmlFor="reg-username"className="block text-sm font-medium text-gray-700">用户名</label>
                            <input
                                type="text" id="reg-username" value={username} onChange={(e) => setUsername(e.target.value)}
                                placeholder="设置一个独特的用户名" required
                                className="mt-1 block w-full px-4 py-3 bg-white/70 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label htmlFor="reg-password"className="block text-sm font-medium text-gray-700">密码</label>
                            <input
                                type="password" id="reg-password" value={password} onChange={(e) => setPassword(e.target.value)}
                                placeholder="设置一个安全的密码 (至少6位)" required
                                className="mt-1 block w-full px-4 py-3 bg-white/70 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        <div>
                            <label htmlFor="confirm-password"className="block text-sm font-medium text-gray-700">确认密码</label>
                            <input
                                type="password" id="confirm-password" value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)}
                                placeholder="请再次输入密码" required
                                className="mt-1 block w-full px-4 py-3 bg-white/70 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        {error && <p className="text-sm text-red-600">{error}</p>}
                        <button type="submit" className="w-full btn-primary" disabled={isLoading}>
                            {isLoading ? '注册中...' : '完成注册'}
                        </button>
                    </form>
                );
            case 'success':
                return (
                    <div className="text-center py-8">
                        <ShieldCheckIcon className="w-20 h-20 text-green-500 mx-auto animate-bounce"/>
                        <h3 className="mt-4 text-2xl font-bold text-gray-800">注册成功!</h3>
                        <p className="mt-2 text-gray-600">欢迎加入我们！系统将在3秒后自动跳转至登录页面。</p>
                        <button onClick={onNavigateToLogin} className="mt-6 w-full btn-primary">立即登录</button>
                    </div>
                );
        }
    }
    
    const illustrationBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA+gAAAMgCAYAAADfH+w0AAAAAXNSR0IArs4c6QAAIABJREFUeF7snQeYJEXVx393d1fXVD1Jk6SJmqQmmrSokV7o2XssFjws9thjsXustV7s2BsLG7sXsaCsmL2AFUEUEVBkRRSUFRFRgohIeyPpzG5ndvfm/M6c2d3ddO/0Aw8z8z5zz5w5c+ad+X2/b86c+Xo2JkYAAAAAgJwScAoAAAAAQE4JGAUAAACAnBIwCgAAAABJyc+3p9vWbB8aP/d8uFvD4LSpC5A7/eGj4U8BfPj0xS/nL7P/6+lTExlZ5P4IqJkzZ87z58+fnz9/PkB/3759+/Xr1z179izT5u3bt4e/ZcWlB93rYjVt2rRp06ZNN2/ePC/J37Zt23nz5g3/3rJly4oVK4aHhaaVlZWmT58+cuTI/v37Z2RkFBQUHDp0KH/Bvn37Bg4cOHbs2AcPHsi/e2Zm5ps3bwYNGjRq1Khbt27nzp3bvXt3v05/0/jll1/effddAP3jjz/s3Lmzd+/e2dnZ2dnZWVlZmZmZGRkZmZmZlZeXnzx5snbt2uTk5ISEhKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKC-AEAcIA";
    
    return (
        <div className="flex h-screen w-screen font-sans auth-background">
            <style>{`
                .btn-primary {
                    @apply flex justify-center py-3 px-4 border border-transparent rounded-md shadow-lg text-lg font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition transform hover:scale-105;
                }
            `}</style>
            <div className="hidden lg:flex w-3/5 flex-col items-start justify-center p-20">
                <div className="max-w-md">
                    <h1 style={{animationDelay: '100ms'}} className="text-5xl font-bold text-gray-800 leading-tight animate-character-reveal">Join Us,</h1>
                    <h1 style={{animationDelay: '250ms'}} className="text-5xl font-bold text-gray-800 leading-tight animate-character-reveal">Start Your Journey!</h1>
                </div>
                <img src={illustrationBase64} alt="Person studying at a desk" className="mt-12 w-full max-w-lg animate-fade-in-fast" style={{animationDelay: '400ms'}} />
            </div>

            <div className="w-full lg:w-2/5 flex items-center justify-center p-8">
                <div className="w-full max-w-sm">
                    <div className="bg-white/40 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/40 animate-fade-in-fast" style={{animationDelay: '500ms'}}>
                        <div className="text-center mb-6">
                            <h2 className="text-3xl font-bold text-gray-800">注册新账号</h2>
                        </div>
                        
                        <div className="mb-8">
                            <ProgressIndicator currentStep={step} />
                        </div>

                        {renderContent()}

                        {step !== 'success' && (
                             <div className="mt-6 text-center text-sm">
                                <p className="text-gray-600">
                                    已经有账号了?{' '}
                                    <a href="#" onClick={(e) => { e.preventDefault(); onNavigateToLogin(); }} className="font-medium text-blue-600 hover:text-blue-500">
                                        前往登录
                                    </a>
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default RegistrationPage;