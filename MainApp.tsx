

import React, { useState, useCallback, useEffect } from 'react';
import Sidebar from './components/Sidebar';
import Header from './components/Header';
import TrainingPage from './components/TrainingPage';
import { HistoryPage } from './components/HistoryPage';
import KnowledgeMapPage from './components/KnowledgeMapPage';
import AssessmentCenterPage from './components/AssessmentCenterPage';
import CompetitionPage from './components/CompetitionPage';
import RightToolbar from './components/RightToolbar';
import AiChatPanel from './components/AiChatPanel';
import ExamPage from './components/ExamPage';
import LoginPage from './components/LoginPage';
import RegistrationPage from './components/RegistrationPage';
import { ActivePage, User, SubjectId, ArchetypeSession, Exam, Question, BreakthroughSessionState } from './types';
import { DEFAULT_USER, TRAINING_TOPICS } from './constants';
import * as api from './services/apiService';


export interface AiChatState {
  isVisible: boolean;
  position: { x: number; y: number };
  size: { width: number; height: number };
  promptInfo: { prompt: string; key: number };
}

const useWindowWidth = () => {
    const [width, setWidth] = useState(window.innerWidth);
    useEffect(() => {
        const handleResize = () => setWidth(window.innerWidth);
        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);
    return width;
};

const MainApp: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authScreen, setAuthScreen] = useState<'login' | 'register'>('login');
  const [isLoadingAuth, setIsLoadingAuth] = useState(true);
  const [activePage, setActivePage] = useState<ActivePage>(ActivePage.AssessmentCenter);
  const [currentSubject, setCurrentSubject] = useState<SubjectId>(SubjectId.HIGHER_MATH);
  const [currentUser, setCurrentUser] = useState<User>(DEFAULT_USER);
  
  const [trainingContext, setTrainingContext] = useState<{ session?: ArchetypeSession, activeBreakthroughSession?: BreakthroughSessionState } | undefined>(undefined);
  const [examSession, setExamSession] = useState<Exam | undefined>(undefined);
  
  const [activeBreakthroughSession, setActiveBreakthroughSession] = useState<BreakthroughSessionState>(null);
  
  const [isAiPanelOpen, setIsAiPanelOpen] = useState(false);
  const [aiPromptInfo, setAiPromptInfo] = useState<{ prompt: string, key: number }>({ prompt: '', key: 0 });
  const [isSidebarManuallyCollapsed, setIsSidebarManuallyCollapsed] = useState(false);
  
  const windowWidth = useWindowWidth();
  const BREAKPOINT_LG = 1024;

  const shouldHideAllSideElements = windowWidth < BREAKPOINT_LG;
  
  const isSidebarVisible = !shouldHideAllSideElements;
  const isRightToolbarVisible = !shouldHideAllSideElements;
  const isSidebarEffectivelyCollapsed = isSidebarManuallyCollapsed;
  const isAiPanelEffectivelyOpen = isAiPanelOpen && !shouldHideAllSideElements;

  const checkAuthentication = useCallback(async () => {
    const token = localStorage.getItem('authToken');
    if (token) {
        try {
            const userInfo = await api.getUserInfo();
            setCurrentUser({ ...DEFAULT_USER, ...userInfo });
            setIsAuthenticated(true);
        } catch (error) {
            console.error("Token validation failed, logging out.", error);
            localStorage.removeItem('authToken');
            setIsAuthenticated(false);
        }
    } else {
        setIsAuthenticated(false);
    }
    setIsLoadingAuth(false);
  }, []);

  useEffect(() => {
    checkAuthentication();
  }, [checkAuthentication]);


  const handleAuthSuccess = useCallback(() => {
    // This function is called by Login/Register pages upon success.
    // It triggers a re-check of authentication state.
    setIsLoadingAuth(true);
    checkAuthentication();
  }, [checkAuthentication]);

  const handleLogout = useCallback(() => {
    localStorage.removeItem('authToken');
    setIsAuthenticated(false);
    setCurrentUser(DEFAULT_USER);
    setAuthScreen('login');
    setActivePage(ActivePage.AssessmentCenter); // Reset to a default page
    setActiveBreakthroughSession(null); // Clear any active session on logout
  }, []);

  const handleToggleSidebar = useCallback(() => {
    setIsSidebarManuallyCollapsed(prev => !prev);
  }, []);

  const handleNavigation = useCallback((page: ActivePage, context?: any) => {
    window.scrollTo(0, 0);
    
    // Logic to save or clear the breakthrough session state
    if (context?.breakthroughToSave) {
        setActiveBreakthroughSession(context.breakthroughToSave);
    } else if (context?.clearBreakthrough) {
        setActiveBreakthroughSession(null);
    }

    setTrainingContext(undefined);
    setExamSession(undefined);

    if (page === ActivePage.TrainingFocus) {
      setTrainingContext({ session: context?.session, activeBreakthroughSession: context?.activeBreakthroughSession });
    } else if (page === ActivePage.ExamTaking && context?.examSession) {
      setExamSession(context.examSession);
    }
    
    setActivePage(page);
  }, []);

  const handleSubjectChange = useCallback((newSubjectId: SubjectId) => {
    setCurrentSubject(newSubjectId);
    handleNavigation(ActivePage.AssessmentCenter);
  }, [handleNavigation]);

  useEffect(() => {
    if (activePage === ActivePage.ExamTaking && !examSession) {
        setActivePage(ActivePage.Competition);
    }
  }, [activePage, examSession]);

  const handleOpenAiPanel = useCallback((prompt: string = '') => {
    setIsAiPanelOpen(true);
    setAiPromptInfo({ prompt, key: Date.now() });
  }, []);

  const handleToggleAiPanel = useCallback(() => {
      setIsAiPanelOpen(p => !p);
  }, []);
  
  const renderActivePage = () => {
    const pageProps = { currentSubject, onNavigate: handleNavigation, user: currentUser };
    const trainingProps = { ...pageProps, onOpenAiPanel: handleOpenAiPanel };

    switch (activePage) {
      case ActivePage.AssessmentCenter:
        return <AssessmentCenterPage {...trainingProps} activeBreakthroughSession={activeBreakthroughSession} />;
      case ActivePage.Competition:
        return <CompetitionPage onNavigate={handleNavigation} />;
      case ActivePage.TrainingFocus:
        return <TrainingPage session={trainingContext?.session} activeBreakthroughSession={trainingContext?.activeBreakthroughSession || activeBreakthroughSession} {...trainingProps} />;
      case ActivePage.History:
        return <HistoryPage {...pageProps} />;
      case ActivePage.KnowledgeMap:
        return <KnowledgeMapPage {...pageProps}/>;
      default:
        return <AssessmentCenterPage {...trainingProps} activeBreakthroughSession={activeBreakthroughSession} />;
    }
  };

  if (isLoadingAuth) {
    return <div className="flex h-screen w-screen items-center justify-center">Loading...</div>;
  }

  if (!isAuthenticated) {
    if (authScreen === 'login') {
        return <LoginPage onLoginSuccess={handleAuthSuccess} onNavigateToRegister={() => setAuthScreen('register')} />;
    } else {
        return <RegistrationPage onRegisterSuccess={handleAuthSuccess} onNavigateToLogin={() => setAuthScreen('login')} />;
    }
  }

  if (activePage === ActivePage.ExamTaking && examSession) {
      return <ExamPage session={examSession} onNavigate={handleNavigation} />;
  }

  return (
    <div className="relative flex h-screen bg-[#F8F9FA] font-sans">
      {isSidebarVisible && (
        <Sidebar 
            activePage={activePage} 
            onNavigate={handleNavigation}
            isCollapsed={isSidebarEffectivelyCollapsed}
            onToggle={handleToggleSidebar}
        />
      )}

      <div className="relative flex-1 flex flex-col overflow-x-hidden">
        <Header 
          activePage={activePage}
          currentSubject={currentSubject}
          onSubjectChange={handleSubjectChange}
          onNavigate={handleNavigation}
        />
        <div className="flex-1 flex flex-row gap-2 py-4 overflow-hidden">
            <main className="flex-1 bg-white rounded-xl shadow-sm overflow-y-auto">
                <div className="min-h-full">
                    {renderActivePage()}
                </div>
            </main>
            <AiChatPanel
                isOpen={isAiPanelEffectivelyOpen}
                onClose={() => setIsAiPanelOpen(false)}
                initialPromptInfo={aiPromptInfo}
            />
        </div>
        
      </div>

      {isRightToolbarVisible && (
        <RightToolbar 
            user={currentUser}
            onToggleAiPanel={handleToggleAiPanel}
            onLogout={handleLogout}
        />
      )}
    </div>
  );
};

export default MainApp;