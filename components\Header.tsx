
import React, { useState } from 'react';
import { ActivePage, User, SubjectId, SubjectDisplayName } from '../types';
import { APP_TITLE_SUFFIX, AVAILABLE_SUBJECTS } from '../constants';
import Dropdown from './Dropdown';
import ChevronDownIcon from './icons/ChevronDownIcon';

interface HeaderProps {
  activePage: ActivePage;
  currentSubject: SubjectId;
  onNavigate: (page: ActivePage, context?: any) => void;
  onSubjectChange: (subjectId: SubjectId) => void;
}

const Header: React.FC<HeaderProps> = ({ 
    activePage, 
    currentSubject, 
    onNavigate, 
    onSubjectChange,
}) => {
  const [isSubjectDropdownOpen, setIsSubjectDropdownOpen] = useState(false);

  const subjectDropdownItems = AVAILABLE_SUBJECTS.map(subject => ({
    label: subject.name,
    onClick: () => {
      onSubjectChange(subject.id);
    }
  }));
  
  const currentSubjectName = SubjectDisplayName[currentSubject] || APP_TITLE_SUFFIX;
  
  const getPageTitle = () => {
    switch(activePage) {
        case ActivePage.AssessmentCenter:
            return '训练';
        case ActivePage.Competition:
            return '考试';
        default:
            return activePage;
    }
  }

  const pageTitle = getPageTitle();

  return (
    <header className="shrink-0 z-10">
      <div className="container mx-auto pl-6 pr-4 py-4">
        <div className="flex justify-between items-center">
          {/* Left Side: Page Title */}
           <div className="flex items-center gap-4">
               <h1 className="text-xl font-semibold text-gray-800">{pageTitle}</h1>
           </div>

          {/* Right Side: Actions */}
          <div className="flex items-center space-x-5">
            <Dropdown
              trigger={
                <button className="text-sm text-gray-600 hover:text-primary-blue flex items-center bg-white px-3 py-2 rounded-md shadow-sm border border-gray-200">
                  <span className="font-medium text-gray-800">{currentSubjectName}</span>
                  <ChevronDownIcon className={`w-4 h-4 ml-1.5 transition-transform duration-200 ${isSubjectDropdownOpen ? 'rotate-180' : '' }`} />
                </button>
              }
              items={subjectDropdownItems}
              dropdownAlign="right"
              onDropdownToggle={setIsSubjectDropdownOpen}
            />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
