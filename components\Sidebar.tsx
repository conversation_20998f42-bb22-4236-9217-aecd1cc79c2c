

import React from 'react';
import { ActivePage } from '../types';
import { AcademicCapIcon, ChartBarIcon, BookOpenIcon, TrophyIcon } from '@heroicons/react/24/solid';
import ChevronLeftIcon from './icons/ChevronLeftIcon';
import ChevronRightIcon from './icons/ChevronRightIcon';

interface SidebarProps {
  activePage: ActivePage;
  onNavigate: (page: ActivePage) => void;
  isCollapsed: boolean;
  onToggle: () => void;
}

const navItemsConfig = [
  { page: ActivePage.AssessmentCenter, icon: AcademicCapIcon, label: '训练' },
  { page: ActivePage.Competition, icon: TrophyIcon, label: ActivePage.Competition },
  { page: ActivePage.History, icon: ChartBarIcon, label: ActivePage.History }, // This is now '数据总览'
  { page: ActivePage.KnowledgeMap, icon: BookOpenIcon, label: ActivePage.KnowledgeMap },
];

const Sidebar: React.FC<SidebarProps> = ({ activePage, onNavigate, isCollapsed, onToggle }) => {
    const appTitle = '智能伴学';

    return (
        <aside className={`relative flex flex-col bg-[#F8F9FA] transition-all duration-300 ease-in-out ${isCollapsed ? 'w-20' : 'w-64'}`}>
            <div className={`flex items-center h-16 shrink-0 border-b border-gray-200 px-6`}>
                <div className={`flex items-center text-blue-600 overflow-hidden`}>
                    <AcademicCapIcon className="w-8 h-8 shrink-0" />
                    <h1 className={`text-2xl font-bold whitespace-nowrap transition-all duration-200 ${isCollapsed ? 'max-w-0 opacity-0' : 'max-w-full opacity-100 ml-3'}`} aria-label={appTitle}>
                        {appTitle}
                    </h1>
                </div>
            </div>

            <nav className="flex-1 px-3 py-4 space-y-2">
                {navItemsConfig.map((item, index) => {
                    const isActive = activePage === item.page;
                    return (
                        <a
                            key={item.page}
                            href="#"
                            onClick={(e) => {
                                e.preventDefault();
                                onNavigate(item.page);
                            }}
                            className={`flex items-center p-3 rounded-lg transition-colors duration-200 group overflow-hidden ${
                                isActive
                                ? 'bg-blue-600 text-white shadow-md'
                                : 'text-gray-600 hover:bg-gray-100'
                            }`}
                            title={isCollapsed ? item.label : undefined}
                        >
                            <item.icon className="w-6 h-6 shrink-0" />
                            <span 
                                style={{ transitionDelay: `${index * 50}ms` }}
                                className={`font-medium text-sm whitespace-nowrap transition-all duration-200 ${isCollapsed ? 'max-w-0 opacity-0' : 'max-w-full opacity-100 ml-4'}`}
                            >
                                {item.label}
                            </span>
                        </a>
                    );
                })}
            </nav>

            <button
                onClick={onToggle}
                className="absolute bottom-6 right-0 z-10 flex h-12 w-12 items-center justify-center rounded-l-full bg-white hover:bg-gray-100 text-gray-600"
                aria-label={isCollapsed ? "展开侧边栏" : "折叠侧边栏"}
            >
                {isCollapsed ? (
                    <ChevronRightIcon className="h-5 w-5" />
                ) : (
                    <ChevronLeftIcon className="h-5 w-5" />
                )}
            </button>
        </aside>
    );
};

export default Sidebar;