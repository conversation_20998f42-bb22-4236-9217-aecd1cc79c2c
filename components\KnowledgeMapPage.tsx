

import React, { useState } from 'react';
import { KnowledgePoint, SubjectId, SubjectDisplayName, ActivePage, ArchetypeSession } from '../types';
import { SUBJECT_KNOWLEDGE_MAP_DATA, TRAINING_TOPICS } from '../constants';
import * as api from '../services/apiService';
import MathRenderer from './MarkdownKatex';
import { ChevronRightIcon, AcademicCapIcon, BoltIcon } from '@heroicons/react/24/solid';

interface KnowledgePointItemProps {
  point: KnowledgePoint;
  level: number;
  isLast: boolean;
  onStartPractice: (kp: KnowledgePoint) => void;
}

const KnowledgePointItem: React.FC<KnowledgePointItemProps> = ({ point, level, isLast, onStartPractice }) => {
  const [isExpanded, setIsExpanded] = useState(level < 1); 
  const toggleExpand = () => setIsExpanded(!isExpanded);

  const hasChildren = point.children && point.children.length > 0;

  return (
    <div className="relative">
        {/* Tree lines */}
        <div className={`absolute top-0 -left-5 w-5 h-full`}>
            {level > 0 && <div className="absolute top-6 left-2 w-px h-full bg-gray-200"></div>}
            {level > 0 && <div className="absolute top-6 left-2 w-3 h-px bg-gray-200"></div>}
        </div>

      <div className={`pl-${level * 5}`}>
        <div 
          className="flex items-center justify-between group cursor-pointer"
          onClick={toggleExpand}
          role="button"
          tabIndex={0}
          onKeyPress={(e) => e.key === 'Enter' && toggleExpand()}
          aria-expanded={isExpanded}
        >
          <div className="flex items-center py-2">
            {hasChildren && <ChevronRightIcon className={`w-4 h-4 mr-2 text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-90' : ''}`} />}
            {!hasChildren && <div className="w-4 h-4 mr-2"></div>}
            <h3 className={`font-semibold ${level === 0 ? 'text-xl text-primary-blue' : 'text-lg text-gray-800'}`}>{point.title}</h3>
          </div>
           <button 
             onClick={(e) => { e.stopPropagation(); onStartPractice(point); }}
             className="bg-blue-100 text-primary-blue text-xs font-bold py-1.5 px-4 rounded-full flex items-center hover:bg-blue-500 hover:text-white transition-all duration-200 transform group-hover:scale-105"
           >
             <BoltIcon className="w-3.5 h-3.5 mr-1" />
             练习
           </button>
        </div>

        {isExpanded && (
            <div className={`pl-6 border-l-2 border-gray-200`}>
                {point.content && (
                    <div className="p-4 my-2 text-gray-700 bg-light-beige rounded-lg border border-yellow-200 shadow-sm prose prose-base max-w-none">
                        <MathRenderer content={point.content} />
                    </div>
                )}
                {hasChildren && (
                    <div className="space-y-2 py-2">
                    {point.children?.map((child, index) => (
                        <KnowledgePointItem 
                            key={child.id} 
                            point={child} 
                            level={level + 1} 
                            isLast={index === point.children!.length - 1}
                            onStartPractice={onStartPractice}
                        />
                    ))}
                    </div>
                )}
            </div>
        )}
      </div>
    </div>
  );
};


interface KnowledgeMapPageProps {
  currentSubject: SubjectId;
  onNavigate: (page: ActivePage, context: any) => void;
}

const KnowledgeMapPage: React.FC<KnowledgeMapPageProps> = ({ currentSubject, onNavigate }) => {
  const subjectName = SubjectDisplayName[currentSubject];
  const knowledgeMapData = SUBJECT_KNOWLEDGE_MAP_DATA[currentSubject] || [];

  const handleStartPractice = async (kp: KnowledgePoint) => {
    try {
        const questions = await api.getQuestionsByKnowledgePoint(kp.id);
        if (questions && questions.length > 0) {
            const firstTopic = TRAINING_TOPICS[currentSubject]?.find(t => t.id === questions[0].topicId) 
                                || TRAINING_TOPICS[currentSubject]?.[0];

            if (!firstTopic) {
                 alert("无法找到与题目关联的专题。");
                 return;
            }

            const session: ArchetypeSession = {
                topic: firstTopic,
                level: questions[0].difficultyLevel || 1,
                questionCount: questions.length,
                totalTimeSeconds: questions.length * 180, // 3 minutes per question average
                questions: questions,
            };
            onNavigate(ActivePage.TrainingFocus, { session });
        } else {
            alert("该知识点下暂无练习题。");
        }
    } catch (error) {
        console.error("Failed to start practice session from knowledge point:", error);
        alert(`开始练习失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };


  return (
    <div className="p-6 md:p-8">
      <div className="bg-white p-6 md:p-8 rounded-2xl shadow-lg">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 flex items-center">
            <AcademicCapIcon className="w-8 h-8 mr-3 text-primary-blue" />
            {subjectName} - 知识体系总览
          </h1>
          <p className="text-gray-500 mt-1">系统化梳理所有知识点，构建你的完整知识网络。</p>
        </div>
        
        {knowledgeMapData.length > 0 ? (
          <div className="space-y-4">
            {knowledgeMapData.map((point, index) => (
              <KnowledgePointItem 
                key={point.id} 
                point={point} 
                level={0} 
                isLast={index === knowledgeMapData.length - 1}
                onStartPractice={handleStartPractice}
               />
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-500 py-16">当前科目 ({subjectName}) 暂无知识体系数据。</p>
        )}
      </div>
    </div>
  );
};

export default KnowledgeMapPage;