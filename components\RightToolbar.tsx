import React from 'react';
import { User } from '../types';
import SparklesIcon from './icons/SparklesIcon';
import Dropdown from './Dropdown';

interface RightToolbarProps {
    user: User;
    onToggleAiPanel: () => void;
    onLogout: () => void;
}

const RightToolbar: React.FC<RightToolbarProps> = ({ user, onToggleAiPanel, onLogout }) => {
    const userDropdownItems = [
        {
            label: '退出账号',
            onClick: onLogout,
        },
    ];

    return (
        <aside className="w-16 flex flex-col items-center shrink-0 bg-[#F8F9FA] z-30">
            <div className="h-16 flex items-center justify-center shrink-0 w-full">
                 <Dropdown
                    trigger={
                        <button className="w-9 h-9 rounded-full overflow-hidden focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                             <img src={user.avatarUrl} alt={user.username} className="w-full h-full object-cover" />
                        </button>
                    }
                    items={userDropdownItems}
                    dropdownAlign="right"
                />
            </div> 
            <div className="p-2 space-y-2 mt-2">
                <button
                    onClick={onToggleAiPanel}
                    className="p-3 rounded-lg text-gray-600 hover:bg-gray-200"
                    title="AI 助教"
                >
                    <SparklesIcon className="w-6 h-6" />
                </button>
            </div>
        </aside>
    );
};

export default RightToolbar;